2024-11-06 10:57:57.846 [main] INFO  c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2024-11-06 10:57:57.923 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2024-11-06 10:58:09.992 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api,DEFAULT_GROUP'}]
2024-11-06 10:58:10.064 [main] INFO  com.jsunicom.api.BcpApiApplication - The following profiles are active: dev
2024-11-06 10:58:13.254 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-11-06 10:58:13.260 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-11-06 10:58:13.610 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 316ms. Found 0 Redis repository interfaces.
2024-11-06 10:58:14.530 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=1e6a9c45-fbac-3ec5-98f2-d963f0d41ba5
2024-11-06 10:58:15.319 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-06 10:58:15.490 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.sleuth.redis-org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties' of type [org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-06 10:58:16.462 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8097 (http)
2024-11-06 10:58:16.495 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8097"]
2024-11-06 10:58:16.497 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-11-06 10:58:16.497 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.29]
2024-11-06 10:58:16.870 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-11-06 10:58:16.871 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6771 ms
2024-11-06 10:58:18.629 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 10:58:18.812 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-06 10:58:18.815 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2024-11-06 10:58:18.818 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-06 11:00:01.010 [main] INFO  c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2024-11-06 11:00:01.082 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2024-11-06 11:00:01.178 [main] INFO  com.alibaba.nacos.client.config.utils.JVMUtil - isMultiInstance:false
2024-11-06 11:00:01.229 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api,DEFAULT_GROUP'}]
2024-11-06 11:00:01.289 [main] INFO  com.jsunicom.api.BcpApiApplication - The following profiles are active: dev
2024-11-06 11:00:04.282 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-11-06 11:00:04.287 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-11-06 11:00:04.479 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 167ms. Found 0 Redis repository interfaces.
2024-11-06 11:00:05.247 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=2354ecdb-b2b7-3069-9e4b-94e94901930a
2024-11-06 11:00:05.766 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-06 11:00:05.875 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.sleuth.redis-org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties' of type [org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-06 11:00:06.307 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8097 (http)
2024-11-06 11:00:06.321 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8097"]
2024-11-06 11:00:06.322 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-11-06 11:00:06.323 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.29]
2024-11-06 11:00:06.559 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-11-06 11:00:06.560 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5216 ms
2024-11-06 11:00:08.135 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:00:08.243 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-06 11:00:08.245 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2024-11-06 11:00:08.246 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-06 11:00:08.955 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService
2024-11-06 11:00:10.214 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-11-06 11:00:10.656 [main] INFO  c.jsunicom.api.service.impl.CaptchaServiceFactory - supported-captchaCache-service:[]
2024-11-06 11:00:10.657 [main] INFO  c.jsunicom.api.service.impl.CaptchaServiceFactory - supported-captchaTypes-service:[]
2024-11-06 11:00:10.697 [main] INFO  com.jsunicom.api.utils.captcha.ImageUtils - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@736f8837, ROTATE=[Ljava.lang.String;@14624acc, ORIGINAL=[Ljava.lang.String;@1b6e4761, PIC_CLICK=[Ljava.lang.String;@f453129, ROTATE_BLOCK=[Ljava.lang.String;@642ec6]
2024-11-06 11:00:10.698 [main] INFO  c.j.api.service.impl.BlockPuzzleCaptchaServiceImpl - --->>>初始化验证码底图<<<---blockPuzzle
2024-11-06 11:00:10.771 [main] INFO  c.j.api.service.impl.BlockPuzzleCaptchaServiceImpl - 初始化local缓存...
2024-11-06 11:00:11.574 [main] INFO  c.j.a.s.impl.whiteList.LoadConfigByNacosService - gateway whitelist init...
2024-11-06 11:00:11.679 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-06 11:00:11.686 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-06 11:00:12.396 [main] INFO  c.a.e.a.c.ElasticsearchLogbackAppenderAutoConfiguration - *********** bcp-api Elasticsearch Appender settings init. es hosts count:3
2024-11-06 11:00:12.431 [main] INFO  c.a.e.a.c.ElasticsearchLogbackAppenderAutoConfiguration - ************* Elasticsearch Appender start and append to logger context.
2024-11-06 11:00:12.450 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'Nacso-Watch-Task-Scheduler'
2024-11-06 11:00:14.041 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:00:15.050 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:00:15.415 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-11-06 11:00:15.557 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8097"]
2024-11-06 11:00:15.591 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8097 (http) with context path ''
2024-11-06 11:00:15.595 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: {"cluster":"DEFAULT","ip":"*************","metadata":{"preserved.register.source":"SPRING_CLOUD"},"period":5000,"port":8097,"scheduled":false,"serviceName":"DEFAULT_GROUP@@bcp-api","stopped":false,"weight":1.0} to beat map.
2024-11-06 11:00:15.913 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-api with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8097,"weight":1.0}
2024-11-06 11:00:15.978 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bcp-api *************:8097 register finished
2024-11-06 11:00:16.984 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:00:16.990 [main] INFO  com.jsunicom.api.BcpApiApplication - Started BcpApiApplication in 20.111 seconds (JVM running for 21.381)
2024-11-06 11:00:17.430 [RMI TCP Connection(5)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-11-06 11:00:17.431 [RMI TCP Connection(5)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-11-06 11:00:17.442 [RMI TCP Connection(4)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-11-06 11:00:17.466 [RMI TCP Connection(5)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 34 ms
2024-11-06 11:00:17.969 [RMI TCP Connection(4)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-11-06 11:00:17.992 [RMI TCP Connection(4)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2024-11-06 11:00:18.031 [RMI TCP Connection(4)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2024-11-06 11:00:18.238 [boundedElastic-1] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-11-06 11:00:18.241 [boundedElastic-1] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-11-06 11:04:30.260 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18888-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:04:31.388 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18888-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:04:31.456 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18888-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api,DEFAULT_GROUP'}]
2024-11-06 11:04:31.477 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18888-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.boot.SpringApplication - The following profiles are active: dev
2024-11-06 11:04:31.529 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18888-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.boot.SpringApplication - Started application in 2.285 seconds (JVM running for 275.935)
2024-11-06 11:04:33.127 [SpringContextShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2024-11-06 11:04:33.129 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@bcp-api:*************:8097 from beat map.
2024-11-06 11:04:33.131 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 deregistering service DEFAULT_GROUP@@bcp-api with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"instanceIdGenerator":"simple","ip":"*************","ipDeleteTimeout":30000,"metadata":{},"port":8097,"weight":1.0}
2024-11-06 11:04:33.141 [SpringContextShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2024-11-06 11:04:33.151 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2024-11-06 11:04:33.279 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService
2024-11-06 11:04:33.289 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown initiated...
2024-11-06 11:04:33.307 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown completed.
2024-11-06 11:04:33.307 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-11-06 11:04:33.324 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2024-11-06 11:04:42.872 [main] INFO  c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2024-11-06 11:04:42.985 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2024-11-06 11:04:43.123 [main] INFO  com.alibaba.nacos.client.config.utils.JVMUtil - isMultiInstance:false
2024-11-06 11:04:43.251 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api,DEFAULT_GROUP'}]
2024-11-06 11:04:43.365 [main] INFO  com.jsunicom.api.BcpApiApplication - The following profiles are active: dev
2024-11-06 11:04:46.891 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-11-06 11:04:46.896 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-11-06 11:04:47.100 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 169ms. Found 0 Redis repository interfaces.
2024-11-06 11:04:47.914 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=2354ecdb-b2b7-3069-9e4b-94e94901930a
2024-11-06 11:04:48.431 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-06 11:04:48.528 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.sleuth.redis-org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties' of type [org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-06 11:04:48.935 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8097 (http)
2024-11-06 11:04:48.947 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8097"]
2024-11-06 11:04:48.948 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-11-06 11:04:48.948 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.29]
2024-11-06 11:04:49.159 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-11-06 11:04:49.160 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5589 ms
2024-11-06 11:04:50.608 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:04:50.744 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-06 11:04:50.745 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2024-11-06 11:04:50.747 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-06 11:04:51.238 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2024-11-06 11:04:51.292 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2024-11-06 11:06:41.316 [main] INFO  c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2024-11-06 11:06:41.358 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2024-11-06 11:06:41.413 [main] INFO  com.alibaba.nacos.client.config.utils.JVMUtil - isMultiInstance:false
2024-11-06 11:06:41.456 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api,DEFAULT_GROUP'}]
2024-11-06 11:06:41.510 [main] INFO  com.jsunicom.api.BcpApiApplication - The following profiles are active: dev
2024-11-06 11:06:43.614 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-11-06 11:06:43.617 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-11-06 11:06:43.723 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 87ms. Found 0 Redis repository interfaces.
2024-11-06 11:06:44.214 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=2354ecdb-b2b7-3069-9e4b-94e94901930a
2024-11-06 11:06:44.605 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-06 11:06:44.698 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.sleuth.redis-org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties' of type [org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-06 11:06:45.059 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8097 (http)
2024-11-06 11:06:45.069 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8097"]
2024-11-06 11:06:45.070 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-11-06 11:06:45.070 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.29]
2024-11-06 11:06:45.286 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-11-06 11:06:45.287 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 3717 ms
2024-11-06 11:06:46.711 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:06:46.808 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-06 11:06:46.809 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2024-11-06 11:06:46.810 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-06 11:06:48.683 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-11-06 11:06:49.095 [main] INFO  c.jsunicom.api.service.impl.CaptchaServiceFactory - supported-captchaCache-service:[]
2024-11-06 11:06:49.096 [main] INFO  c.jsunicom.api.service.impl.CaptchaServiceFactory - supported-captchaTypes-service:[]
2024-11-06 11:06:49.145 [main] INFO  com.jsunicom.api.utils.captcha.ImageUtils - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@33089426, ROTATE=[Ljava.lang.String;@129c322f, ORIGINAL=[Ljava.lang.String;@1bbef3c7, PIC_CLICK=[Ljava.lang.String;@35995029, ROTATE_BLOCK=[Ljava.lang.String;@42474b18]
2024-11-06 11:06:49.146 [main] INFO  c.j.api.service.impl.BlockPuzzleCaptchaServiceImpl - --->>>初始化验证码底图<<<---blockPuzzle
2024-11-06 11:06:49.174 [main] INFO  c.j.api.service.impl.BlockPuzzleCaptchaServiceImpl - 初始化local缓存...
2024-11-06 11:06:49.941 [main] INFO  c.j.a.s.impl.whiteList.LoadConfigByNacosService - gateway whitelist init...
2024-11-06 11:06:50.045 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-06 11:06:50.052 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-06 11:06:50.703 [main] INFO  c.a.e.a.c.ElasticsearchLogbackAppenderAutoConfiguration - *********** bcp-api Elasticsearch Appender settings init. es hosts count:3
2024-11-06 11:06:50.735 [main] INFO  c.a.e.a.c.ElasticsearchLogbackAppenderAutoConfiguration - ************* Elasticsearch Appender start and append to logger context.
2024-11-06 11:06:50.754 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'Nacso-Watch-Task-Scheduler'
2024-11-06 11:06:52.406 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:06:53.414 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:06:53.676 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-11-06 11:06:53.807 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8097"]
2024-11-06 11:06:53.850 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8097 (http) with context path ''
2024-11-06 11:06:53.855 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: {"cluster":"DEFAULT","ip":"*************","metadata":{"preserved.register.source":"SPRING_CLOUD"},"period":5000,"port":8097,"scheduled":false,"serviceName":"DEFAULT_GROUP@@bcp-api","stopped":false,"weight":1.0} to beat map.
2024-11-06 11:06:54.163 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-api with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8097,"weight":1.0}
2024-11-06 11:06:54.178 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bcp-api *************:8097 register finished
2024-11-06 11:06:55.186 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:06:55.189 [main] INFO  com.jsunicom.api.BcpApiApplication - Started BcpApiApplication in 17.469 seconds (JVM running for 18.456)
2024-11-06 11:06:55.736 [RMI TCP Connection(5)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-11-06 11:06:55.738 [RMI TCP Connection(5)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-11-06 11:06:55.740 [RMI TCP Connection(4)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-11-06 11:06:55.781 [RMI TCP Connection(5)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 43 ms
2024-11-06 11:06:56.327 [RMI TCP Connection(4)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-11-06 11:06:56.348 [RMI TCP Connection(4)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2024-11-06 11:06:56.383 [RMI TCP Connection(4)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2024-11-06 11:06:56.543 [boundedElastic-1] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-11-06 11:06:56.545 [boundedElastic-1] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-11-06 11:10:53.728 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18888-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:10:54.877 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18888-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:10:54.979 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18888-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api,DEFAULT_GROUP'}]
2024-11-06 11:10:55.024 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18888-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.boot.SpringApplication - The following profiles are active: dev
2024-11-06 11:10:55.058 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18888-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.boot.SpringApplication - Started application in 2.346 seconds (JVM running for 258.32)
2024-11-06 11:10:55.419 [SpringContextShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2024-11-06 11:10:55.428 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@bcp-api:*************:8097 from beat map.
2024-11-06 11:10:55.429 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 deregistering service DEFAULT_GROUP@@bcp-api with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"instanceIdGenerator":"simple","ip":"*************","ipDeleteTimeout":30000,"metadata":{},"port":8097,"weight":1.0}
2024-11-06 11:10:55.439 [SpringContextShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2024-11-06 11:10:55.457 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2024-11-06 11:10:55.608 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown initiated...
2024-11-06 11:10:55.628 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown completed.
2024-11-06 11:10:55.628 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-11-06 11:11:08.306 [main] INFO  c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2024-11-06 11:11:08.389 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2024-11-06 11:11:08.472 [main] INFO  com.alibaba.nacos.client.config.utils.JVMUtil - isMultiInstance:false
2024-11-06 11:11:08.534 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api,DEFAULT_GROUP'}]
2024-11-06 11:11:08.619 [main] INFO  com.jsunicom.api.BcpApiApplication - The following profiles are active: dev
2024-11-06 11:11:12.845 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-11-06 11:11:12.850 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-11-06 11:11:13.061 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 187ms. Found 0 Redis repository interfaces.
2024-11-06 11:11:14.042 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=2354ecdb-b2b7-3069-9e4b-94e94901930a
2024-11-06 11:11:14.783 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-06 11:11:14.915 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.sleuth.redis-org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties' of type [org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-06 11:11:15.544 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8097 (http)
2024-11-06 11:11:15.564 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8097"]
2024-11-06 11:11:15.565 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-11-06 11:11:15.566 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.29]
2024-11-06 11:11:15.957 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-11-06 11:11:15.958 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7252 ms
2024-11-06 11:11:17.640 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:11:17.794 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-06 11:11:17.796 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2024-11-06 11:11:17.798 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-06 11:11:19.832 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-11-06 11:11:20.249 [main] INFO  c.jsunicom.api.service.impl.CaptchaServiceFactory - supported-captchaCache-service:[]
2024-11-06 11:11:20.250 [main] INFO  c.jsunicom.api.service.impl.CaptchaServiceFactory - supported-captchaTypes-service:[]
2024-11-06 11:11:20.299 [main] INFO  com.jsunicom.api.utils.captcha.ImageUtils - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@27b7204, ROTATE=[Ljava.lang.String;@5895c065, ORIGINAL=[Ljava.lang.String;@683fac7e, PIC_CLICK=[Ljava.lang.String;@2051a0ec, ROTATE_BLOCK=[Ljava.lang.String;@79631db0]
2024-11-06 11:11:20.300 [main] INFO  c.j.api.service.impl.BlockPuzzleCaptchaServiceImpl - --->>>初始化验证码底图<<<---blockPuzzle
2024-11-06 11:11:20.316 [main] INFO  c.j.api.service.impl.BlockPuzzleCaptchaServiceImpl - 初始化local缓存...
2024-11-06 11:11:21.034 [main] INFO  c.j.a.s.impl.whiteList.LoadConfigByNacosService - gateway whitelist init...
2024-11-06 11:11:21.148 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-06 11:11:21.153 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-06 11:11:21.901 [main] INFO  c.a.e.a.c.ElasticsearchLogbackAppenderAutoConfiguration - *********** bcp-api Elasticsearch Appender settings init. es hosts count:3
2024-11-06 11:11:21.937 [main] INFO  c.a.e.a.c.ElasticsearchLogbackAppenderAutoConfiguration - ************* Elasticsearch Appender start and append to logger context.
2024-11-06 11:11:21.957 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'Nacso-Watch-Task-Scheduler'
2024-11-06 11:11:23.573 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:11:24.583 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:11:24.867 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-11-06 11:11:25.007 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8097"]
2024-11-06 11:11:25.044 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8097 (http) with context path ''
2024-11-06 11:11:25.048 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: {"cluster":"DEFAULT","ip":"*************","metadata":{"preserved.register.source":"SPRING_CLOUD"},"period":5000,"port":8097,"scheduled":false,"serviceName":"DEFAULT_GROUP@@bcp-api","stopped":false,"weight":1.0} to beat map.
2024-11-06 11:11:25.360 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-api with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8097,"weight":1.0}
2024-11-06 11:11:25.372 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bcp-api *************:8097 register finished
2024-11-06 11:11:26.378 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:11:26.382 [main] INFO  com.jsunicom.api.BcpApiApplication - Started BcpApiApplication in 24.177 seconds (JVM running for 27.137)
2024-11-06 11:11:26.505 [RMI TCP Connection(6)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-11-06 11:11:26.512 [RMI TCP Connection(6)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-11-06 11:11:26.519 [RMI TCP Connection(7)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-11-06 11:11:26.552 [RMI TCP Connection(6)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 40 ms
2024-11-06 11:11:27.126 [RMI TCP Connection(7)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-11-06 11:11:27.153 [RMI TCP Connection(7)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2024-11-06 11:11:27.196 [RMI TCP Connection(7)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2024-11-06 11:11:27.449 [boundedElastic-1] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-11-06 11:11:27.451 [boundedElastic-1] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-11-06 11:12:27.465 [SpringContextShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2024-11-06 11:12:27.466 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@bcp-api:*************:8097 from beat map.
2024-11-06 11:12:27.467 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 deregistering service DEFAULT_GROUP@@bcp-api with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"instanceIdGenerator":"simple","ip":"*************","ipDeleteTimeout":30000,"metadata":{},"port":8097,"weight":1.0}
2024-11-06 11:12:27.474 [SpringContextShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2024-11-06 11:12:27.490 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2024-11-06 11:12:27.702 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown initiated...
2024-11-06 11:12:27.724 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown completed.
2024-11-06 11:12:27.725 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-11-06 11:12:27.747 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2024-11-06 11:12:36.414 [main] INFO  c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2024-11-06 11:12:36.453 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2024-11-06 11:12:36.506 [main] INFO  com.alibaba.nacos.client.config.utils.JVMUtil - isMultiInstance:false
2024-11-06 11:12:36.530 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api,DEFAULT_GROUP'}]
2024-11-06 11:12:36.566 [main] INFO  com.jsunicom.api.BcpApiApplication - The following profiles are active: dev
2024-11-06 11:12:38.314 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-11-06 11:12:38.317 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-11-06 11:12:38.425 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 92ms. Found 0 Redis repository interfaces.
2024-11-06 11:12:38.950 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=2354ecdb-b2b7-3069-9e4b-94e94901930a
2024-11-06 11:12:39.401 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-06 11:12:39.498 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.sleuth.redis-org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties' of type [org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-06 11:12:46.241 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8097 (http)
2024-11-06 11:12:46.254 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8097"]
2024-11-06 11:12:46.255 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-11-06 11:12:46.256 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.29]
2024-11-06 11:12:46.463 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-11-06 11:12:46.464 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 9861 ms
2024-11-06 11:12:47.982 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:12:48.122 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-06 11:12:48.124 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2024-11-06 11:12:48.126 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-06 11:12:50.532 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-11-06 11:12:51.099 [main] INFO  c.jsunicom.api.service.impl.CaptchaServiceFactory - supported-captchaCache-service:[]
2024-11-06 11:12:51.101 [main] INFO  c.jsunicom.api.service.impl.CaptchaServiceFactory - supported-captchaTypes-service:[]
2024-11-06 11:12:51.148 [main] INFO  com.jsunicom.api.utils.captcha.ImageUtils - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@67fd2e17, ROTATE=[Ljava.lang.String;@65a2e14e, ORIGINAL=[Ljava.lang.String;@5e9f1a4c, PIC_CLICK=[Ljava.lang.String;@4bf89d3d, ROTATE_BLOCK=[Ljava.lang.String;@457b8fc3]
2024-11-06 11:12:51.149 [main] INFO  c.j.api.service.impl.BlockPuzzleCaptchaServiceImpl - --->>>初始化验证码底图<<<---blockPuzzle
2024-11-06 11:12:51.170 [main] INFO  c.j.api.service.impl.BlockPuzzleCaptchaServiceImpl - 初始化local缓存...
2024-11-06 11:12:52.058 [main] INFO  c.j.a.s.impl.whiteList.LoadConfigByNacosService - gateway whitelist init...
2024-11-06 11:20:54.946 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18888-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  c.j.a.s.impl.whiteList.LoadConfigByNacosService - getExecutor

2024-11-06 11:20:54.949 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18888-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  c.j.a.s.impl.whiteList.LoadConfigByNacosService - 进行白名单更新:

noAuthorizationRequest:
  - /test/*
noAuthenticationRequest:
  - /wechat/**
  - /userAction/**
  - /partner/**
  - /org/department/**
  - /captcha/**
  - /sms/**
  - /scYiMember/queryInfoBySocietyId
  - /scYiMember/queryMajor
  - /scYiMember/queryDormitory
  - /scYiMember/addPresident
  - /leader/queryLeaderInfoForAdd
  - /leader/addLeader
  - /qcsMember/queryInfoForAddQCSMember
  - /qcsMember/addQCSMember
  - /woschool/promotion/**
  - /dict/getDictsByKind
noEncryptRequest:
  - /captcha/**
  - /wechat/**
2024-11-06 11:21:17.321 [main] INFO  c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2024-11-06 11:21:17.390 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2024-11-06 11:21:17.467 [main] INFO  com.alibaba.nacos.client.config.utils.JVMUtil - isMultiInstance:false
2024-11-06 11:21:17.496 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api,DEFAULT_GROUP'}]
2024-11-06 11:21:17.545 [main] INFO  com.jsunicom.api.BcpApiApplication - The following profiles are active: dev
2024-11-06 11:21:22.826 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-11-06 11:21:22.829 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-11-06 11:21:22.947 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 102ms. Found 0 Redis repository interfaces.
2024-11-06 11:21:23.618 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=2354ecdb-b2b7-3069-9e4b-94e94901930a
2024-11-06 11:21:24.226 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-06 11:21:24.341 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.sleuth.redis-org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties' of type [org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-06 11:21:24.927 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8097 (http)
2024-11-06 11:21:24.959 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8097"]
2024-11-06 11:21:24.961 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-11-06 11:21:24.961 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.29]
2024-11-06 11:21:25.296 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-11-06 11:21:25.297 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7712 ms
2024-11-06 11:21:26.771 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:21:26.870 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-06 11:21:26.871 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2024-11-06 11:21:26.872 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-06 11:21:28.995 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-11-06 11:21:29.504 [main] INFO  c.jsunicom.api.service.impl.CaptchaServiceFactory - supported-captchaCache-service:[]
2024-11-06 11:21:29.505 [main] INFO  c.jsunicom.api.service.impl.CaptchaServiceFactory - supported-captchaTypes-service:[]
2024-11-06 11:21:29.619 [main] INFO  com.jsunicom.api.utils.captcha.ImageUtils - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@65f470f8, ROTATE=[Ljava.lang.String;@531299d3, ORIGINAL=[Ljava.lang.String;@3487947c, PIC_CLICK=[Ljava.lang.String;@77226121, ROTATE_BLOCK=[Ljava.lang.String;@3117ac8d]
2024-11-06 11:21:29.621 [main] INFO  c.j.api.service.impl.BlockPuzzleCaptchaServiceImpl - --->>>初始化验证码底图<<<---blockPuzzle
2024-11-06 11:21:29.678 [main] INFO  c.j.api.service.impl.BlockPuzzleCaptchaServiceImpl - 初始化local缓存...
2024-11-06 11:21:30.623 [main] INFO  c.j.a.s.impl.whiteList.LoadConfigByNacosService - gateway whitelist init...
2024-11-06 11:21:33.195 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-06 11:21:33.204 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-06 11:21:34.119 [main] INFO  c.a.e.a.c.ElasticsearchLogbackAppenderAutoConfiguration - *********** bcp-api Elasticsearch Appender settings init. es hosts count:3
2024-11-06 11:21:34.150 [main] INFO  c.a.e.a.c.ElasticsearchLogbackAppenderAutoConfiguration - ************* Elasticsearch Appender start and append to logger context.
2024-11-06 11:21:34.170 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'Nacso-Watch-Task-Scheduler'
2024-11-06 11:21:35.748 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:21:36.779 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:21:37.161 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-11-06 11:21:37.304 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8097"]
2024-11-06 11:21:37.385 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8097 (http) with context path ''
2024-11-06 11:21:37.389 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: {"cluster":"DEFAULT","ip":"*************","metadata":{"preserved.register.source":"SPRING_CLOUD"},"period":5000,"port":8097,"scheduled":false,"serviceName":"DEFAULT_GROUP@@bcp-api","stopped":false,"weight":1.0} to beat map.
2024-11-06 11:21:37.413 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-api with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8097,"weight":1.0}
2024-11-06 11:21:37.426 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bcp-api *************:8097 register finished
2024-11-06 11:21:38.433 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-06 11:21:38.437 [main] INFO  com.jsunicom.api.BcpApiApplication - Started BcpApiApplication in 38.972 seconds (JVM running for 40.554)
2024-11-06 11:21:38.808 [RMI TCP Connection(9)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-11-06 11:21:38.810 [RMI TCP Connection(8)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-11-06 11:21:38.811 [RMI TCP Connection(8)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-11-06 11:21:38.845 [RMI TCP Connection(8)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 34 ms
2024-11-06 11:21:39.430 [RMI TCP Connection(9)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-11-06 11:21:39.453 [RMI TCP Connection(9)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2024-11-06 11:21:39.490 [RMI TCP Connection(9)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2024-11-06 11:21:39.664 [boundedElastic-1] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-11-06 11:21:39.667 [boundedElastic-1] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-11-06 11:31:39.932 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16379
2024-11-06 11:31:39.943 [lettuce-nioEventLoop-4-2] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16379
2024-11-06 11:41:40.001 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16379
2024-11-06 11:41:40.008 [lettuce-nioEventLoop-4-3] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16379
2024-11-06 11:51:40.072 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16379
2024-11-06 11:51:40.079 [lettuce-nioEventLoop-4-4] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16379
2024-11-06 12:01:40.151 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16379
2024-11-06 12:01:40.169 [lettuce-nioEventLoop-4-5] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16379
2024-11-06 12:11:40.269 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16379
2024-11-06 12:11:40.276 [lettuce-nioEventLoop-4-6] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16379
2024-11-06 12:21:40.314 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16379
2024-11-06 12:21:40.322 [lettuce-nioEventLoop-4-7] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16379
2024-11-06 12:31:40.394 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16379
2024-11-06 12:31:40.399 [lettuce-nioEventLoop-4-8] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16379
2024-11-06 12:41:40.456 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16379
2024-11-06 12:41:40.474 [lettuce-nioEventLoop-4-1] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16379
2024-11-06 12:51:40.525 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16379
2024-11-06 12:51:40.532 [lettuce-nioEventLoop-4-2] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16379
2024-11-06 13:01:40.633 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16379
2024-11-06 13:01:40.638 [lettuce-nioEventLoop-4-3] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16379
2024-11-06 13:11:40.709 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16379
2024-11-06 13:11:40.713 [lettuce-nioEventLoop-4-4] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16379
2024-11-06 13:21:40.791 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16379
2024-11-06 13:21:40.795 [lettuce-nioEventLoop-4-5] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16379
2024-11-06 13:31:40.846 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16379
2024-11-06 13:31:40.851 [lettuce-nioEventLoop-4-6] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16379
2024-11-06 13:41:40.919 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16379
2024-11-06 13:41:40.928 [lettuce-nioEventLoop-4-7] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16379
2024-11-06 13:51:40.980 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16379
2024-11-06 13:51:40.985 [lettuce-nioEventLoop-4-8] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16379
2024-11-06 13:59:44.733 [SpringContextShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2024-11-06 13:59:44.741 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@bcp-api:*************:8097 from beat map.
2024-11-06 13:59:44.746 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 deregistering service DEFAULT_GROUP@@bcp-api with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"instanceIdGenerator":"simple","ip":"*************","ipDeleteTimeout":30000,"metadata":{},"port":8097,"weight":1.0}
2024-11-06 13:59:44.779 [SpringContextShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2024-11-06 13:59:44.840 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2024-11-07 14:35:22.270 [main] INFO  c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2024-11-07 14:35:22.355 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2024-11-07 14:35:22.475 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api,DEFAULT_GROUP'}]
2024-11-07 14:35:22.601 [main] INFO  com.jsunicom.api.BcpApiApplication - The following profiles are active: dev
2024-11-07 14:35:30.044 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-11-07 14:35:30.052 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-11-07 14:35:30.507 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 405ms. Found 0 Redis repository interfaces.
2024-11-07 14:35:31.813 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=1e6a9c45-fbac-3ec5-98f2-d963f0d41ba5
2024-11-07 14:35:32.873 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 14:35:33.030 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.sleuth.redis-org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties' of type [org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 14:35:33.751 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8097 (http)
2024-11-07 14:35:33.781 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8097"]
2024-11-07 14:35:33.782 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-11-07 14:35:33.783 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.29]
2024-11-07 14:35:34.154 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-11-07 14:35:34.155 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 11477 ms
2024-11-07 14:35:36.141 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 14:35:36.362 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 14:35:36.363 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2024-11-07 14:35:36.364 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 14:35:37.910 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2024-11-07 14:35:38.051 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2024-11-07 15:39:24.117 [main] INFO  c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2024-11-07 15:39:24.168 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2024-11-07 15:39:24.215 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-oms-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms,DEFAULT_GROUP'}]
2024-11-07 15:39:24.276 [main] INFO  com.jsunicom.oms.BcpOmsApplication - The following profiles are active: dev
2024-11-07 15:39:27.003 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-11-07 15:39:27.008 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-11-07 15:39:27.456 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 423ms. Found 0 Redis repository interfaces.
2024-11-07 15:39:28.362 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=2bde9cb1-764f-3da0-9365-ebe7d3f27b34
2024-11-07 15:39:28.993 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 15:39:29.235 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.sleuth.redis-org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties' of type [org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 15:39:29.979 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8096 (http)
2024-11-07 15:39:30.005 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8096"]
2024-11-07 15:39:30.006 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-11-07 15:39:30.007 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.29]
2024-11-07 15:39:30.364 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/oms-bcp] - Initializing Spring embedded WebApplicationContext
2024-11-07 15:39:30.365 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6058 ms
2024-11-07 15:39:32.172 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:39:32.988 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 15:39:32.989 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2024-11-07 15:39:32.991 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 15:39:40.613 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2024-11-07 15:39:40.736 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2024-11-07 15:41:43.960 [main] INFO  c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2024-11-07 15:41:44.037 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2024-11-07 15:41:44.273 [main] INFO  com.alibaba.nacos.client.config.utils.JVMUtil - isMultiInstance:false
2024-11-07 15:41:44.330 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-oms-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms,DEFAULT_GROUP'}]
2024-11-07 15:41:44.439 [main] INFO  com.jsunicom.oms.BcpOmsApplication - The following profiles are active: dev
2024-11-07 15:41:47.945 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-11-07 15:41:47.949 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-11-07 15:41:48.147 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 173ms. Found 0 Redis repository interfaces.
2024-11-07 15:41:48.942 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=2bde9cb1-764f-3da0-9365-ebe7d3f27b34
2024-11-07 15:41:49.583 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 15:41:49.745 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.sleuth.redis-org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties' of type [org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 15:41:50.381 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8096 (http)
2024-11-07 15:41:50.408 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8096"]
2024-11-07 15:41:50.409 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-11-07 15:41:50.410 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.29]
2024-11-07 15:41:50.775 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/oms-bcp] - Initializing Spring embedded WebApplicationContext
2024-11-07 15:41:50.775 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6285 ms
2024-11-07 15:41:52.417 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:41:52.638 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 15:41:52.640 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2024-11-07 15:41:52.641 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 15:41:53.300 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2024-11-07 15:41:53.344 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2024-11-07 15:42:55.077 [main] INFO  c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2024-11-07 15:42:55.130 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2024-11-07 15:42:55.372 [main] INFO  com.alibaba.nacos.client.config.utils.JVMUtil - isMultiInstance:false
2024-11-07 15:42:55.504 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-oms-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms,DEFAULT_GROUP'}]
2024-11-07 15:42:55.655 [main] INFO  com.jsunicom.oms.BcpOmsApplication - The following profiles are active: dev
2024-11-07 15:43:00.671 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-11-07 15:43:00.676 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-11-07 15:43:00.923 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 222ms. Found 0 Redis repository interfaces.
2024-11-07 15:43:01.649 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=2bde9cb1-764f-3da0-9365-ebe7d3f27b34
2024-11-07 15:43:02.343 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 15:43:02.498 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.sleuth.redis-org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties' of type [org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 15:43:03.185 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8096 (http)
2024-11-07 15:43:03.201 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8096"]
2024-11-07 15:43:03.202 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-11-07 15:43:03.202 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.29]
2024-11-07 15:43:03.491 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/oms-bcp] - Initializing Spring embedded WebApplicationContext
2024-11-07 15:43:03.491 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7783 ms
2024-11-07 15:43:05.046 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:43:05.156 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 15:43:05.157 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2024-11-07 15:43:05.157 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 15:43:13.201 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-11-07 15:43:16.196 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-11-07 15:43:16.747 [main] INFO  com.jsunicom.oms.config.XxlJobConfig - >>>>>>>>>>> xxl-job config init.
2024-11-07 15:43:16.817 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-07 15:43:16.831 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-07 15:43:18.137 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'taskScheduler'
2024-11-07 15:43:20.139 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:43:21.149 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:43:21.793 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:autoAddWaterMarkJob, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@721384e0[class com.jsunicom.oms.timer.order.AutoAddWaterMarkJob$$EnhancerBySpringCGLIB$$2f417bf2#autoAddWaterMarkJob]
2024-11-07 15:43:21.794 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:autoCloseMakePhotoJob, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@a15e4fd[class com.jsunicom.oms.timer.order.AutoCloseMakePhotoJob#autoCloseMakePhotoJob]
2024-11-07 15:43:21.794 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:autoCloseNoActiveOrderJob, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@515c3dd2[class com.jsunicom.oms.timer.order.AutoCloseNoActiveOrderJob#autoCloseNoActiveOrderJob]
2024-11-07 15:43:21.794 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:autoPayJob, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@245504d[class com.jsunicom.oms.timer.order.AutoPayJob#autoPayJob]
2024-11-07 15:43:21.794 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:autoReActivationJob, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6c9fe061[class com.jsunicom.oms.timer.order.AutoReActivationJob#autoReActivationJob]
2024-11-07 15:43:21.795 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:autoSynSchoolOrderDetailJob, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4988c303[class com.jsunicom.oms.timer.order.AutoSynSchoolOrderDetailJob#autoSynSchoolOrderDetailJob]
2024-11-07 15:43:21.795 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:CCPShopToDBJob, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@798ac54d[class com.jsunicom.oms.timer.order.CCPShopToDBJob#cCPShopToDBJob]
2024-11-07 15:43:21.795 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:downloadIntoDBJob, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@51653509[class com.jsunicom.oms.timer.order.DownloadIntoDBJob#downloadIntoDBJob]
2024-11-07 15:43:21.796 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:kongWarnJob, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@42ea4bac[class com.jsunicom.oms.timer.order.KongWarnJob$$EnhancerBySpringCGLIB$$2c309e21#kongWarnJob]
2024-11-07 15:43:21.796 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:payResultConfirmJob, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4df50829[class com.jsunicom.oms.timer.order.PayResultConfirmJob$$EnhancerBySpringCGLIB$$a7ba6b49#payResultConfirmJob]
2024-11-07 15:43:21.796 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:schoolOrderHistoryReport, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@627a1c77[class com.jsunicom.oms.timer.order.SchoolOrderHistoryReportJob#synSchoolOrderReport]
2024-11-07 15:43:21.796 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:smsAndFaceAbilityJob, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1acfb04c[class com.jsunicom.oms.timer.order.SmsAndFaceAbilityJob#smsAndFaceAbilityJob]
2024-11-07 15:43:21.796 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:synCampusOrderItemJob, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@a683f92[class com.jsunicom.oms.timer.order.SynCampusOrderItemJob#synCampusOrderItemJob]
2024-11-07 15:43:21.796 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:synZRRPhotoJob, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6fb9dab6[class com.jsunicom.oms.timer.order.SynZRRPhotoJob$$EnhancerBySpringCGLIB$$701aa516#synZRRPhotoJob]
2024-11-07 15:43:21.797 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:synZRRPhotoRspJob, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5d12fa92[class com.jsunicom.oms.timer.order.SynZRRPhotoRspJob$$EnhancerBySpringCGLIB$$d27690f5#synZRRPhotoRspJob]
2024-11-07 15:43:21.798 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:testImgJob, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@71a9e31b[class com.jsunicom.oms.timer.order.TestImgJob#testImg]
2024-11-07 15:43:21.798 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:testXxlJobTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@18e7b33e[class com.jsunicom.oms.timer.order.XxxlJobTest#testXxlJobTask]
2024-11-07 15:43:21.798 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:changeSerialByConditionJob, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3b386ba7[class com.jsunicom.oms.timer.resource.ChangeSerialByConditionJob#changeSerialByConditionJob]
2024-11-07 15:43:21.799 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:deleteSerialIdle, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5a41183b[class com.jsunicom.oms.timer.resource.ClearSerialNumberJob#deteleSerialIdle]
2024-11-07 15:43:21.799 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:resetOccupiedNumber, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@644e9953[class com.jsunicom.oms.timer.resource.ResetOccupiedNumberJob#resetOccupiedNumber]
2024-11-07 15:43:21.799 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:sendMsgByTime, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4115b8a7[class com.jsunicom.oms.timer.resource.SendMsgJob#sendMsgByTime]
2024-11-07 15:43:23.252 [Thread-82] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2024-11-07 15:43:27.275 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='oms-svc', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(connect timed out), for url : http://************:38080/xxl-job-admin/api/registry, content=null]
2024-11-07 15:43:27.661 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8096"]
2024-11-07 15:43:27.738 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8096 (http) with context path '/oms-bcp'
2024-11-07 15:43:27.761 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: {"cluster":"DEFAULT","ip":"*************","metadata":{"preserved.register.source":"SPRING_CLOUD"},"period":5000,"port":8096,"scheduled":false,"serviceName":"DEFAULT_GROUP@@bcp-oms","stopped":false,"weight":1.0} to beat map.
2024-11-07 15:43:27.822 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8096,"weight":1.0}
2024-11-07 15:43:28.100 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bcp-oms *************:8096 register finished
2024-11-07 15:43:29.106 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:43:29.111 [main] INFO  com.jsunicom.oms.BcpOmsApplication - Started BcpOmsApplication in 38.602 seconds (JVM running for 42.67)
2024-11-07 15:43:29.652 [RMI TCP Connection(4)-127.0.0.1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/oms-bcp] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-11-07 15:43:29.652 [RMI TCP Connection(4)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-11-07 15:43:29.709 [RMI TCP Connection(4)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 57 ms
2024-11-07 15:43:29.816 [RMI TCP Connection(3)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-11-07 15:43:31.146 [RMI TCP Connection(3)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-11-07 15:43:31.229 [RMI TCP Connection(3)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2024-11-07 15:43:31.707 [RMI TCP Connection(3)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2024-11-07 15:43:32.101 [boundedElastic-1] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-11-07 15:43:32.140 [boundedElastic-1] INFO  io.lettuce.core.KqueueProvider - Starting with kqueue library
2024-11-07 15:44:01.281 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='oms-svc', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(connect timed out), for url : http://************:38080/xxl-job-admin/api/registry, content=null]
2024-11-07 15:44:35.288 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='oms-svc', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(connect timed out), for url : http://************:38080/xxl-job-admin/api/registry, content=null]
2024-11-07 15:45:09.291 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='oms-svc', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(connect timed out), for url : http://************:38080/xxl-job-admin/api/registry, content=null]
2024-11-07 15:45:43.295 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='oms-svc', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(connect timed out), for url : http://************:38080/xxl-job-admin/api/registry, content=null]
2024-11-07 15:46:17.303 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='oms-svc', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(connect timed out), for url : http://************:38080/xxl-job-admin/api/registry, content=null]
2024-11-07 15:46:51.308 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='oms-svc', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(connect timed out), for url : http://************:38080/xxl-job-admin/api/registry, content=null]
2024-11-07 15:47:25.313 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='oms-svc', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(connect timed out), for url : http://************:38080/xxl-job-admin/api/registry, content=null]
2024-11-07 15:47:59.315 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='oms-svc', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(connect timed out), for url : http://************:38080/xxl-job-admin/api/registry, content=null]
2024-11-07 15:48:33.320 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='oms-svc', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(connect timed out), for url : http://************:38080/xxl-job-admin/api/registry, content=null]
2024-11-07 15:49:07.325 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='oms-svc', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(connect timed out), for url : http://************:38080/xxl-job-admin/api/registry, content=null]
2024-11-07 15:49:22.668 [com.alibaba.nacos.naming.beat.sender] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8096,"serviceName":"DEFAULT_GROUP@@bcp-oms","weight":1.0}
2024-11-07 15:49:41.328 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='oms-svc', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(connect timed out), for url : http://************:38080/xxl-job-admin/api/registry, content=null]
2024-11-07 15:50:15.333 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='oms-svc', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(connect timed out), for url : http://************:38080/xxl-job-admin/api/registry, content=null]
2024-11-07 15:50:49.344 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='oms-svc', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(connect timed out), for url : http://************:38080/xxl-job-admin/api/registry, content=null]
2024-11-07 15:51:23.355 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='oms-svc', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(connect timed out), for url : http://************:38080/xxl-job-admin/api/registry, content=null]
2024-11-07 15:51:57.367 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='oms-svc', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(connect timed out), for url : http://************:38080/xxl-job-admin/api/registry, content=null]
2024-11-07 15:52:31.370 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='oms-svc', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(connect timed out), for url : http://************:38080/xxl-job-admin/api/registry, content=null]
2024-11-07 15:53:05.377 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='oms-svc', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(connect timed out), for url : http://************:38080/xxl-job-admin/api/registry, content=null]
2024-11-07 15:53:39.381 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='oms-svc', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(connect timed out), for url : http://************:38080/xxl-job-admin/api/registry, content=null]
2024-11-07 15:53:42.326 [SpringContextShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2024-11-07 15:53:42.326 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@bcp-oms:*************:8096 from beat map.
2024-11-07 15:53:42.327 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 deregistering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"instanceIdGenerator":"simple","ip":"*************","ipDeleteTimeout":30000,"metadata":{},"port":8096,"weight":1.0}
2024-11-07 15:53:42.396 [SpringContextShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2024-11-07 15:53:42.399 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
2024-11-07 15:53:42.407 [Thread-82] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2024-11-07 15:55:33.567 [main] INFO  c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2024-11-07 15:55:33.614 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2024-11-07 15:55:33.805 [main] INFO  com.alibaba.nacos.client.config.utils.JVMUtil - isMultiInstance:false
2024-11-07 15:55:33.840 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-oms-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms,DEFAULT_GROUP'}]
2024-11-07 15:55:33.923 [main] INFO  com.jsunicom.oms.BcpOmsApplication - The following profiles are active: dev
2024-11-07 15:55:37.112 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-11-07 15:55:37.116 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-11-07 15:55:37.300 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 167ms. Found 0 Redis repository interfaces.
2024-11-07 15:55:38.007 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=910dcfc4-df09-3e0d-b863-a1d487b6d31d
2024-11-07 15:55:38.624 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 15:55:38.786 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.sleuth.redis-org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties' of type [org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 15:55:39.264 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8096 (http)
2024-11-07 15:55:39.276 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8096"]
2024-11-07 15:55:39.277 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-11-07 15:55:39.277 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.29]
2024-11-07 15:55:39.478 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/oms-bcp] - Initializing Spring embedded WebApplicationContext
2024-11-07 15:55:39.478 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5520 ms
2024-11-07 15:55:41.042 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:55:41.186 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 15:55:41.188 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2024-11-07 15:55:41.189 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 15:55:51.751 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-11-07 15:55:55.757 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-11-07 15:55:56.174 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-07 15:55:56.185 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-07 15:55:57.494 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'taskScheduler'
2024-11-07 15:55:59.656 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:56:00.666 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:56:03.973 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8096"]
2024-11-07 15:56:04.022 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8096 (http) with context path '/oms-bcp'
2024-11-07 15:56:04.028 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: {"cluster":"DEFAULT","ip":"*************","metadata":{"preserved.register.source":"SPRING_CLOUD"},"period":5000,"port":8096,"scheduled":false,"serviceName":"DEFAULT_GROUP@@bcp-oms","stopped":false,"weight":1.0} to beat map.
2024-11-07 15:56:04.078 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8096,"weight":1.0}
2024-11-07 15:56:04.252 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bcp-oms *************:8096 register finished
2024-11-07 15:56:05.259 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:56:05.263 [main] INFO  com.jsunicom.oms.BcpOmsApplication - Started BcpOmsApplication in 36.372 seconds (JVM running for 37.822)
2024-11-07 15:56:05.873 [RMI TCP Connection(8)-127.0.0.1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/oms-bcp] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-11-07 15:56:05.874 [RMI TCP Connection(8)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-11-07 15:56:05.913 [RMI TCP Connection(8)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 39 ms
2024-11-07 15:56:05.919 [RMI TCP Connection(7)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-11-07 15:56:06.444 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:56:07.318 [RMI TCP Connection(7)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-11-07 15:56:07.383 [RMI TCP Connection(7)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2024-11-07 15:56:07.565 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:56:07.821 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-oms-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms,DEFAULT_GROUP'}]
2024-11-07 15:56:07.856 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.boot.SpringApplication - The following profiles are active: dev
2024-11-07 15:56:07.876 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.boot.SpringApplication - Started application in 2.44 seconds (JVM running for 40.435)
2024-11-07 15:56:07.918 [RMI TCP Connection(7)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2024-11-07 15:56:08.306 [boundedElastic-1] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-11-07 15:56:08.365 [boundedElastic-1] INFO  io.lettuce.core.KqueueProvider - Starting with kqueue library
2024-11-07 15:56:38.452 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:56:39.557 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:56:39.864 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-oms-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms,DEFAULT_GROUP'}]
2024-11-07 15:56:39.913 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.boot.SpringApplication - The following profiles are active: dev
2024-11-07 15:56:39.944 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.boot.SpringApplication - Started application in 2.528 seconds (JVM running for 72.504)
2024-11-07 15:57:10.652 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:57:11.827 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:57:12.130 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-oms-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms,DEFAULT_GROUP'}]
2024-11-07 15:57:12.162 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.boot.SpringApplication - The following profiles are active: dev
2024-11-07 15:57:12.184 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.boot.SpringApplication - Started application in 2.549 seconds (JVM running for 104.746)
2024-11-07 15:57:43.103 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:57:44.201 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:57:44.414 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-oms-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms,DEFAULT_GROUP'}]
2024-11-07 15:57:44.448 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.boot.SpringApplication - The following profiles are active: dev
2024-11-07 15:57:44.467 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  org.springframework.boot.SpringApplication - Started application in 2.373 seconds (JVM running for 137.03)
2024-11-07 15:58:03.091 [SpringContextShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2024-11-07 15:58:03.093 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@bcp-oms:*************:8096 from beat map.
2024-11-07 15:58:03.094 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 deregistering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"instanceIdGenerator":"simple","ip":"*************","ipDeleteTimeout":30000,"metadata":{},"port":8096,"weight":1.0}
2024-11-07 15:58:03.145 [SpringContextShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2024-11-07 15:58:03.147 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
2024-11-07 15:58:12.711 [main] INFO  c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2024-11-07 15:58:12.750 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2024-11-07 15:58:12.945 [main] INFO  com.alibaba.nacos.client.config.utils.JVMUtil - isMultiInstance:false
2024-11-07 15:58:12.983 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-oms-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms,DEFAULT_GROUP'}]
2024-11-07 15:58:13.063 [main] INFO  com.jsunicom.oms.BcpOmsApplication - The following profiles are active: dev
2024-11-07 15:58:15.880 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-11-07 15:58:15.883 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-11-07 15:58:16.069 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 167ms. Found 0 Redis repository interfaces.
2024-11-07 15:58:16.784 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=910dcfc4-df09-3e0d-b863-a1d487b6d31d
2024-11-07 15:58:17.315 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 15:58:17.467 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.sleuth.redis-org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties' of type [org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 15:58:17.977 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8096 (http)
2024-11-07 15:58:17.992 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8096"]
2024-11-07 15:58:17.993 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-11-07 15:58:17.994 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.29]
2024-11-07 15:58:18.192 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/oms-bcp] - Initializing Spring embedded WebApplicationContext
2024-11-07 15:58:18.192 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5103 ms
2024-11-07 15:58:19.757 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:58:19.874 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 15:58:19.875 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2024-11-07 15:58:19.876 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 15:58:26.518 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-11-07 15:58:28.612 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-11-07 15:58:28.941 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-07 15:58:28.950 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-07 15:58:29.691 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'taskScheduler'
2024-11-07 15:58:31.384 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:58:32.397 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:58:35.426 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8096"]
2024-11-07 15:58:35.477 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8096 (http) with context path '/oms-bcp'
2024-11-07 15:58:35.483 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: {"cluster":"DEFAULT","ip":"*************","metadata":{"preserved.register.source":"SPRING_CLOUD"},"period":5000,"port":8096,"scheduled":false,"serviceName":"DEFAULT_GROUP@@bcp-oms","stopped":false,"weight":1.0} to beat map.
2024-11-07 15:58:35.520 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8096,"weight":1.0}
2024-11-07 15:58:35.676 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bcp-oms *************:8096 register finished
2024-11-07 15:58:36.684 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 15:58:36.688 [main] INFO  com.jsunicom.oms.BcpOmsApplication - Started BcpOmsApplication in 28.227 seconds (JVM running for 30.269)
2024-11-07 15:58:37.006 [RMI TCP Connection(3)-127.0.0.1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/oms-bcp] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-11-07 15:58:37.007 [RMI TCP Connection(3)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-11-07 15:58:37.034 [RMI TCP Connection(3)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 27 ms
2024-11-07 15:58:37.174 [RMI TCP Connection(2)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-11-07 15:58:38.634 [RMI TCP Connection(2)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-11-07 15:58:38.717 [RMI TCP Connection(2)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2024-11-07 15:58:39.453 [RMI TCP Connection(2)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2024-11-07 15:58:39.870 [boundedElastic-1] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-11-07 15:58:39.910 [boundedElastic-1] INFO  io.lettuce.core.KqueueProvider - Starting with kqueue library
2024-11-07 16:08:40.298 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 16:08:40.320 [lettuce-kqueueEventLoop-4-2] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 16:09:15.925 [main] INFO  c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2024-11-07 16:09:16.557 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2024-11-07 16:09:17.494 [main] INFO  com.alibaba.nacos.client.config.utils.JVMUtil - isMultiInstance:false
2024-11-07 16:09:17.536 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api,DEFAULT_GROUP'}]
2024-11-07 16:09:17.873 [main] INFO  com.jsunicom.api.BcpApiApplication - The following profiles are active: dev
2024-11-07 16:09:47.630 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-11-07 16:09:47.699 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-11-07 16:09:50.560 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 2770ms. Found 0 Redis repository interfaces.
2024-11-07 16:09:56.424 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=d730c3f5-ebdb-3938-a012-0aa97631c848
2024-11-07 16:10:00.831 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 16:10:01.774 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.sleuth.redis-org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties' of type [org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 16:10:04.312 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8097 (http)
2024-11-07 16:10:04.345 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8097"]
2024-11-07 16:10:04.347 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-11-07 16:10:04.349 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.29]
2024-11-07 16:10:05.951 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-11-07 16:10:05.952 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 47972 ms
2024-11-07 16:10:09.701 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 16:10:10.027 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 16:10:10.029 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2024-11-07 16:10:10.030 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 16:10:18.157 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-11-07 16:10:20.065 [main] INFO  c.jsunicom.api.service.impl.CaptchaServiceFactory - supported-captchaCache-service:[]
2024-11-07 16:10:20.067 [main] INFO  c.jsunicom.api.service.impl.CaptchaServiceFactory - supported-captchaTypes-service:[]
2024-11-07 16:10:20.198 [main] INFO  com.jsunicom.api.utils.captcha.ImageUtils - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@183a508b, ROTATE=[Ljava.lang.String;@5aeebccc, ORIGINAL=[Ljava.lang.String;@14293540, PIC_CLICK=[Ljava.lang.String;@6fe243a, ROTATE_BLOCK=[Ljava.lang.String;@4c8a9266]
2024-11-07 16:10:20.208 [main] INFO  c.j.api.service.impl.BlockPuzzleCaptchaServiceImpl - --->>>初始化验证码底图<<<---blockPuzzle
2024-11-07 16:10:20.423 [main] INFO  c.j.api.service.impl.BlockPuzzleCaptchaServiceImpl - 初始化local缓存...
2024-11-07 16:10:24.501 [main] INFO  c.j.a.s.impl.whiteList.LoadConfigByNacosService - gateway whitelist init...
2024-11-07 16:10:25.090 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-07 16:10:25.102 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-07 16:10:28.336 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'Nacso-Watch-Task-Scheduler'
2024-11-07 16:10:32.053 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 16:10:33.063 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 16:10:33.947 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-11-07 16:10:34.597 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8097"]
2024-11-07 16:10:34.726 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8097 (http) with context path ''
2024-11-07 16:10:34.745 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: {"cluster":"DEFAULT","ip":"*************","metadata":{"preserved.register.source":"SPRING_CLOUD"},"period":5000,"port":8097,"scheduled":false,"serviceName":"DEFAULT_GROUP@@bcp-api","stopped":false,"weight":1.0} to beat map.
2024-11-07 16:10:34.845 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-api with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8097,"weight":1.0}
2024-11-07 16:10:35.533 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bcp-api *************:8097 register finished
2024-11-07 16:10:36.539 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 16:10:36.543 [main] INFO  com.jsunicom.api.BcpApiApplication - Started BcpApiApplication in 100.116 seconds (JVM running for 106.07)
2024-11-07 16:10:36.862 [RMI TCP Connection(3)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-11-07 16:10:36.864 [RMI TCP Connection(3)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-11-07 16:10:36.911 [RMI TCP Connection(3)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 47 ms
2024-11-07 16:10:37.057 [RMI TCP Connection(2)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-11-07 16:10:38.230 [RMI TCP Connection(2)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-11-07 16:10:38.307 [RMI TCP Connection(2)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2024-11-07 16:10:39.490 [RMI TCP Connection(2)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2024-11-07 16:10:39.865 [boundedElastic-1] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-11-07 16:10:39.869 [boundedElastic-1] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-11-07 16:18:40.663 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 16:18:40.698 [lettuce-kqueueEventLoop-4-3] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 16:20:40.560 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16002
2024-11-07 16:20:40.681 [lettuce-nioEventLoop-4-2] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 16:28:40.834 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 16:28:40.865 [lettuce-kqueueEventLoop-4-4] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 16:30:41.117 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16002
2024-11-07 16:30:41.175 [lettuce-nioEventLoop-4-3] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 16:38:41.001 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 16:38:41.037 [lettuce-kqueueEventLoop-4-5] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 16:40:41.385 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16002
2024-11-07 16:40:41.429 [lettuce-nioEventLoop-4-4] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 16:48:41.277 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 16:48:41.312 [lettuce-kqueueEventLoop-4-6] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 16:50:41.656 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16002
2024-11-07 16:50:41.687 [lettuce-nioEventLoop-4-5] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 16:58:41.542 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 16:58:41.572 [lettuce-kqueueEventLoop-4-7] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 17:00:41.926 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16002
2024-11-07 17:00:41.978 [lettuce-nioEventLoop-4-6] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 17:08:41.709 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 17:08:41.753 [lettuce-kqueueEventLoop-4-8] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 17:10:42.295 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16002
2024-11-07 17:10:42.319 [lettuce-nioEventLoop-4-7] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 17:18:42.076 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 17:18:42.123 [lettuce-kqueueEventLoop-4-1] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 17:20:42.562 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:16002
2024-11-07 17:20:42.587 [lettuce-nioEventLoop-4-8] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 17:53:22.755 [com.alibaba.nacos.naming.beat.sender] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8096,"serviceName":"DEFAULT_GROUP@@bcp-oms","weight":1.0}
2024-11-07 17:53:22.858 [com.alibaba.nacos.naming.beat.sender] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-api with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8097,"serviceName":"DEFAULT_GROUP@@bcp-api","weight":1.0}
2024-11-07 17:54:15.596 [com.alibaba.nacos.naming.beat.sender] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-api with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8097,"serviceName":"DEFAULT_GROUP@@bcp-api","weight":1.0}
2024-11-07 18:03:16.532 [SpringContextShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2024-11-07 18:03:16.543 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@bcp-api:*************:8097 from beat map.
2024-11-07 18:03:16.545 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 deregistering service DEFAULT_GROUP@@bcp-api with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"instanceIdGenerator":"simple","ip":"*************","ipDeleteTimeout":30000,"metadata":{},"port":8097,"weight":1.0}
2024-11-07 18:03:16.617 [SpringContextShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2024-11-07 18:03:16.804 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2024-11-07 18:03:17.331 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown initiated...
2024-11-07 18:03:17.372 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown completed.
2024-11-07 18:03:17.372 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-11-07 18:03:17.413 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2024-11-07 18:03:34.077 [SpringContextShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2024-11-07 18:03:34.079 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@bcp-oms:*************:8096 from beat map.
2024-11-07 18:03:34.080 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 deregistering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"instanceIdGenerator":"simple","ip":"*************","ipDeleteTimeout":30000,"metadata":{},"port":8096,"weight":1.0}
2024-11-07 18:03:34.142 [SpringContextShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2024-11-07 18:03:34.150 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
2024-11-07 18:03:34.235 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2024-11-07 18:03:34.486 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown initiated...
2024-11-07 18:03:34.493 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown completed.
2024-11-07 18:03:34.494 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2024-11-07 18:03:34.498 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2024-11-07 18:03:51.265 [main] INFO  c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2024-11-07 18:03:51.311 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2024-11-07 18:03:51.513 [main] INFO  com.alibaba.nacos.client.config.utils.JVMUtil - isMultiInstance:false
2024-11-07 18:03:51.559 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-api-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-api,DEFAULT_GROUP'}]
2024-11-07 18:03:51.667 [main] INFO  com.jsunicom.api.BcpApiApplication - The following profiles are active: dev
2024-11-07 18:03:55.887 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-11-07 18:03:55.894 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-11-07 18:03:56.136 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 220ms. Found 0 Redis repository interfaces.
2024-11-07 18:03:56.800 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=d730c3f5-ebdb-3938-a012-0aa97631c848
2024-11-07 18:03:57.346 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 18:03:57.467 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.sleuth.redis-org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties' of type [org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 18:03:58.036 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8097 (http)
2024-11-07 18:03:58.055 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8097"]
2024-11-07 18:03:58.057 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-11-07 18:03:58.057 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.29]
2024-11-07 18:03:58.464 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-11-07 18:03:58.464 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6763 ms
2024-11-07 18:03:59.997 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 18:04:00.118 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 18:04:00.119 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2024-11-07 18:04:00.120 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 18:04:03.308 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-11-07 18:04:04.010 [main] INFO  c.jsunicom.api.service.impl.CaptchaServiceFactory - supported-captchaCache-service:[]
2024-11-07 18:04:04.011 [main] INFO  c.jsunicom.api.service.impl.CaptchaServiceFactory - supported-captchaTypes-service:[]
2024-11-07 18:04:04.123 [main] INFO  com.jsunicom.api.utils.captcha.ImageUtils - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@6fad5ace, ROTATE=[Ljava.lang.String;@632dc41, ORIGINAL=[Ljava.lang.String;@35f6f105, PIC_CLICK=[Ljava.lang.String;@3aa1c45, ROTATE_BLOCK=[Ljava.lang.String;@6d45dd4]
2024-11-07 18:04:04.124 [main] INFO  c.j.api.service.impl.BlockPuzzleCaptchaServiceImpl - --->>>初始化验证码底图<<<---blockPuzzle
2024-11-07 18:04:04.283 [main] INFO  c.j.api.service.impl.BlockPuzzleCaptchaServiceImpl - 初始化local缓存...
2024-11-07 18:04:05.425 [main] INFO  c.j.a.s.impl.whiteList.LoadConfigByNacosService - gateway whitelist init...
2024-11-07 18:04:05.635 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-07 18:04:05.643 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-07 18:04:06.582 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'Nacso-Watch-Task-Scheduler'
2024-11-07 18:04:08.703 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 18:04:09.716 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 18:04:10.094 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-11-07 18:04:10.339 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8097"]
2024-11-07 18:04:10.747 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8097 (http) with context path ''
2024-11-07 18:04:10.752 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: {"cluster":"DEFAULT","ip":"*************","metadata":{"preserved.register.source":"SPRING_CLOUD"},"period":5000,"port":8097,"scheduled":false,"serviceName":"DEFAULT_GROUP@@bcp-api","stopped":false,"weight":1.0} to beat map.
2024-11-07 18:04:10.785 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-api with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8097,"weight":1.0}
2024-11-07 18:04:10.953 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bcp-api *************:8097 register finished
2024-11-07 18:04:11.958 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 18:04:11.961 [main] INFO  com.jsunicom.api.BcpApiApplication - Started BcpApiApplication in 24.501 seconds (JVM running for 27.441)
2024-11-07 18:04:12.423 [RMI TCP Connection(3)-127.0.0.1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-11-07 18:04:12.425 [RMI TCP Connection(3)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-11-07 18:04:12.457 [RMI TCP Connection(3)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 32 ms
2024-11-07 18:04:12.576 [RMI TCP Connection(2)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-11-07 18:04:13.453 [RMI TCP Connection(2)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-11-07 18:04:13.520 [RMI TCP Connection(2)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2024-11-07 18:04:14.025 [RMI TCP Connection(2)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2024-11-07 18:04:14.321 [boundedElastic-1] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-11-07 18:04:14.323 [boundedElastic-1] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2024-11-07 18:05:26.312 [SpringContextShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2024-11-07 18:05:26.314 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@bcp-api:*************:8097 from beat map.
2024-11-07 18:05:26.314 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 deregistering service DEFAULT_GROUP@@bcp-api with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"instanceIdGenerator":"simple","ip":"*************","ipDeleteTimeout":30000,"metadata":{},"port":8097,"weight":1.0}
2024-11-07 18:05:26.385 [SpringContextShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2024-11-07 18:05:26.406 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2024-11-07 19:16:06.386 [main] INFO  c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2024-11-07 19:16:06.463 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2024-11-07 19:16:06.701 [main] INFO  com.alibaba.nacos.client.config.utils.JVMUtil - isMultiInstance:false
2024-11-07 19:16:06.770 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-oms-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms,DEFAULT_GROUP'}]
2024-11-07 19:16:06.987 [main] INFO  com.jsunicom.oms.BcpOmsApplication - The following profiles are active: dev
2024-11-07 19:16:13.075 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-11-07 19:16:13.088 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-11-07 19:16:13.560 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 439ms. Found 0 Redis repository interfaces.
2024-11-07 19:16:14.968 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=910dcfc4-df09-3e0d-b863-a1d487b6d31d
2024-11-07 19:16:16.155 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 19:16:16.668 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.sleuth.redis-org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties' of type [org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 19:16:18.536 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8096 (http)
2024-11-07 19:16:18.557 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8096"]
2024-11-07 19:16:18.559 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-11-07 19:16:18.560 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.29]
2024-11-07 19:16:19.191 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/oms-bcp] - Initializing Spring embedded WebApplicationContext
2024-11-07 19:16:19.191 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 12151 ms
2024-11-07 19:16:21.796 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 19:16:21.993 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 19:16:21.995 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2024-11-07 19:16:21.996 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 19:16:33.378 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-11-07 19:16:37.602 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-11-07 19:16:38.400 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-07 19:16:38.425 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-07 19:16:39.600 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'taskScheduler'
2024-11-07 19:16:41.916 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 19:16:42.926 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 19:16:47.252 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8096"]
2024-11-07 19:16:47.310 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8096 (http) with context path '/oms-bcp'
2024-11-07 19:16:47.318 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: {"cluster":"DEFAULT","ip":"*************","metadata":{"preserved.register.source":"SPRING_CLOUD"},"period":5000,"port":8096,"scheduled":false,"serviceName":"DEFAULT_GROUP@@bcp-oms","stopped":false,"weight":1.0} to beat map.
2024-11-07 19:16:47.374 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8096,"weight":1.0}
2024-11-07 19:16:48.202 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bcp-oms *************:8096 register finished
2024-11-07 19:16:49.216 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 19:16:49.223 [main] INFO  com.jsunicom.oms.BcpOmsApplication - Started BcpOmsApplication in 51.128 seconds (JVM running for 54.142)
2024-11-07 19:16:49.773 [RMI TCP Connection(14)-127.0.0.1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/oms-bcp] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-11-07 19:16:49.776 [RMI TCP Connection(14)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-11-07 19:16:49.810 [RMI TCP Connection(14)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 34 ms
2024-11-07 19:16:49.944 [RMI TCP Connection(12)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-11-07 19:16:50.891 [RMI TCP Connection(12)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-11-07 19:16:50.960 [RMI TCP Connection(12)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2024-11-07 19:16:52.094 [RMI TCP Connection(12)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2024-11-07 19:16:52.424 [boundedElastic-1] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-11-07 19:16:52.469 [boundedElastic-1] INFO  io.lettuce.core.KqueueProvider - Starting with kqueue library
2024-11-07 19:22:50.272 [com.alibaba.nacos.naming.beat.sender] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8096,"serviceName":"DEFAULT_GROUP@@bcp-oms","weight":1.0}
2024-11-07 19:52:18.993 [com.alibaba.nacos.naming.beat.sender] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8096,"serviceName":"DEFAULT_GROUP@@bcp-oms","weight":1.0}
2024-11-07 20:17:34.180 [com.alibaba.nacos.naming.beat.sender] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8096,"serviceName":"DEFAULT_GROUP@@bcp-oms","weight":1.0}
2024-11-07 20:29:41.875 [com.alibaba.nacos.naming.beat.sender] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8096,"serviceName":"DEFAULT_GROUP@@bcp-oms","weight":1.0}
2024-11-07 20:33:48.737 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 20:33:48.738 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 20:33:48.746 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJzZXJpYWxOdW1iZXIiOiIxNTY1MTYxMDI4OSIsInN0YWZmTm8iOiJaMDAwTEpYMSIsInN0YWZmQ2xhc3MiOiIxIiwic2V4IjoiMSIsImRpbWlzc2lvblRhZyI6IjAiLCJkZXBhcnRLaW5kVHlwZSI6IjQiLCJhcmVhQ29kZSI6IiIsIm5jU2VyaWFsTnVtYmVyIjoiMTU2NTE2MTAyODkiLCJwcm92aW5jZSI6IjM0Iiwic3RhZmZOYW1lIjoi5YiY6YeR6ZGrIiwidXNlclBpZCI6IjQxMjcyODE5OTEwODEwMDAzMiIsImRlcGFydENvZGUiOiIzNDU2NzAyIiwiZGVwYXJ0T3JDaG5sTmFtZSI6Iuaxn+iLj+ecgeWIhuWFrOWPuOS/oeaBr+WMlumDqCJ9==
2024-11-07 20:33:49.853 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 20:33:49.854 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 20:33:50.137 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 20:33:50.137 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2F
2024-11-07 20:33:50.137 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - staffNo:Z000LJX1
2024-11-07 20:43:50.717 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 20:43:50.852 [lettuce-kqueueEventLoop-4-3] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 20:53:51.190 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 20:53:51.232 [lettuce-kqueueEventLoop-4-4] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:01:07.069 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:01:07.073 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:01:07.075 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJzZXJpYWxOdW1iZXIiOiIxNTY1MTYxMDI4OSIsInN0YWZmTm8iOiJaMDAwTEpYMSIsInN0YWZmQ2xhc3MiOiIxIiwic2V4IjoiMSIsImRpbWlzc2lvblRhZyI6IjAiLCJkZXBhcnRLaW5kVHlwZSI6IjQiLCJhcmVhQ29kZSI6IiIsIm5jU2VyaWFsTnVtYmVyIjoiMTU2NTE2MTAyODkiLCJwcm92aW5jZSI6IjM0Iiwic3RhZmZOYW1lIjoi5YiY6YeR6ZGrIiwidXNlclBpZCI6IjQxMjcyODE5OTEwODEwMDAzMiIsImRlcGFydENvZGUiOiIzNDU2NzAyIiwiZGVwYXJ0T3JDaG5sTmFtZSI6Iuaxn+iLj+ecgeWIhuWFrOWPuOS/oeaBr+WMlumDqCJ9==
2024-11-07 21:01:07.916 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:01:07.919 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:01:07.962 [http-nio-8096-exec-2] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:01:07.964 [http-nio-8096-exec-2] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2F
2024-11-07 21:01:07.966 [http-nio-8096-exec-2] INFO  c.j.oms.controller.OperationPermissionController - staffNo:Z000LJX1
2024-11-07 21:04:56.938 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[]
2024-11-07 21:05:26.493 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[]
2024-11-07 21:05:56.050 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[]
2024-11-07 21:06:25.609 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[]
2024-11-07 21:06:55.160 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[]
2024-11-07 21:06:55.562 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:06:55.568 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:07:02.145 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJzZXJpYWxOdW1iZXIiOiIxNTY1MTYxMDI4OSIsInN0YWZmTm8iOiJaMDAwTEpYMSIsInN0YWZmQ2xhc3MiOiIxIiwic2V4IjoiMSIsImRpbWlzc2lvblRhZyI6IjAiLCJkZXBhcnRLaW5kVHlwZSI6IjQiLCJhcmVhQ29kZSI6IiIsIm5jU2VyaWFsTnVtYmVyIjoiMTU2NTE2MTAyODkiLCJwcm92aW5jZSI6IjM0Iiwic3RhZmZOYW1lIjoi5YiY6YeR6ZGrIiwidXNlclBpZCI6IjQxMjcyODE5OTEwODEwMDAzMiIsImRlcGFydENvZGUiOiIzNDU2NzAyIiwiZGVwYXJ0T3JDaG5sTmFtZSI6Iuaxn+iLj+ecgeWIhuWFrOWPuOS/oeaBr+WMlumDqCJ9==
2024-11-07 21:11:23.891 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[]
2024-11-07 21:11:24.053 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 21:11:24.096 [lettuce-kqueueEventLoop-4-5] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:11:24.412 [com.alibaba.nacos.naming.beat.sender] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8096,"serviceName":"DEFAULT_GROUP@@bcp-oms","weight":1.0}
2024-11-07 21:11:24.702 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:11:24.702 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:11:24.710 [http-nio-8096-exec-3] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:11:24.710 [http-nio-8096-exec-3] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2F
2024-11-07 21:11:24.710 [http-nio-8096-exec-3] INFO  c.j.oms.controller.OperationPermissionController - staffNo:Z000LJX1
2024-11-07 21:11:53.949 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[]
2024-11-07 21:12:23.503 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[]
2024-11-07 21:12:53.053 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[]
2024-11-07 21:13:22.607 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[]
2024-11-07 21:13:52.171 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[]
2024-11-07 21:14:22.149 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[]
2024-11-07 21:14:51.716 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[]
2024-11-07 21:15:21.273 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[]
2024-11-07 21:15:50.823 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[]
2024-11-07 21:16:20.497 [com.alibaba.nacos.client.Worker.longPolling.fixed-************_18848-78fe3dfc-fd29-4d3e-8200-52d9b014a563] INFO  com.alibaba.nacos.client.config.impl.ClientWorker - get changedGroupKeys:[]
2024-11-07 21:16:55.850 [main] INFO  c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2024-11-07 21:16:55.916 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2024-11-07 21:16:56.128 [main] INFO  com.alibaba.nacos.client.config.utils.JVMUtil - isMultiInstance:false
2024-11-07 21:16:56.176 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-oms-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms,DEFAULT_GROUP'}]
2024-11-07 21:16:56.283 [main] INFO  com.jsunicom.oms.BcpOmsApplication - The following profiles are active: dev
2024-11-07 21:17:07.997 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-11-07 21:17:08.001 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-11-07 21:17:08.282 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 262ms. Found 0 Redis repository interfaces.
2024-11-07 21:17:09.146 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=910dcfc4-df09-3e0d-b863-a1d487b6d31d
2024-11-07 21:17:09.898 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 21:17:10.111 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.sleuth.redis-org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties' of type [org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 21:17:10.838 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8096 (http)
2024-11-07 21:17:10.855 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8096"]
2024-11-07 21:17:10.856 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-11-07 21:17:10.856 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.29]
2024-11-07 21:17:11.232 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/oms-bcp] - Initializing Spring embedded WebApplicationContext
2024-11-07 21:17:11.232 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 14913 ms
2024-11-07 21:17:12.883 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 21:17:13.063 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 21:17:13.065 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2024-11-07 21:17:13.066 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 21:17:19.804 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-11-07 21:17:22.056 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-11-07 21:17:22.382 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-07 21:17:22.388 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-07 21:17:23.102 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'taskScheduler'
2024-11-07 21:17:24.889 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 21:17:25.899 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 21:17:29.409 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8096"]
2024-11-07 21:17:29.546 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8096 (http) with context path '/oms-bcp'
2024-11-07 21:17:29.562 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: {"cluster":"DEFAULT","ip":"*************","metadata":{"preserved.register.source":"SPRING_CLOUD"},"period":5000,"port":8096,"scheduled":false,"serviceName":"DEFAULT_GROUP@@bcp-oms","stopped":false,"weight":1.0} to beat map.
2024-11-07 21:17:29.649 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8096,"weight":1.0}
2024-11-07 21:17:29.794 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bcp-oms *************:8096 register finished
2024-11-07 21:17:30.799 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 21:17:30.803 [main] INFO  com.jsunicom.oms.BcpOmsApplication - Started BcpOmsApplication in 39.915 seconds (JVM running for 42.872)
2024-11-07 21:17:31.367 [RMI TCP Connection(7)-127.0.0.1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/oms-bcp] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-11-07 21:17:31.369 [RMI TCP Connection(7)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-11-07 21:17:31.432 [RMI TCP Connection(7)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 63 ms
2024-11-07 21:17:31.841 [RMI TCP Connection(6)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-11-07 21:17:33.216 [RMI TCP Connection(6)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-11-07 21:17:33.288 [RMI TCP Connection(6)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2024-11-07 21:17:33.877 [RMI TCP Connection(6)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2024-11-07 21:17:34.400 [boundedElastic-1] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-11-07 21:17:34.511 [boundedElastic-1] INFO  io.lettuce.core.KqueueProvider - Starting with kqueue library
2024-11-07 21:17:52.270 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:17:52.270 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:17:52.272 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJzZXJpYWxOdW1iZXIiOiIxNTY1MTIzMDUwNiIsInN0YWZmTm8iOiJKU0lEMDAwMiIsInN0YWZmQ2xhc3MiOiIxIiwic2V4IjoiMSIsImRpbWlzc2lvblRhZyI6IjAiLCJkZXBhcnRLaW5kVHlwZSI6IjMiLCJhcmVhQ29kZSI6IjQ0MCIsIm5jU2VyaWFsTnVtYmVyIjoiMTU2NTEyMzA1MDYiLCJwcm92aW5jZSI6IjM0Iiwic3RhZmZOYW1lIjoi5bi45bee6ZmG5bu66IuxIiwidXNlclBpZCI6IjMyMDQwMjE5NzkwMTEwMzEyMSIsImRlcGFydENvZGUiOiIzNDEwNDcyIiwiZGVwYXJ0T3JDaG5sTmFtZSI6IuW4uOW3nuW4guWIhuWFrOWPuCJ9==
2024-11-07 21:19:51.721 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:19:51.723 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:19:51.825 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:19:51.825 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2F
2024-11-07 21:19:51.825 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - staffNo:JSID0002
2024-11-07 21:19:56.754 [com.alibaba.nacos.naming.beat.sender] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8096,"serviceName":"DEFAULT_GROUP@@bcp-oms","weight":1.0}
2024-11-07 21:25:07.128 [SpringContextShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2024-11-07 21:25:07.131 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [BEAT] removing beat: DEFAULT_GROUP@@bcp-oms:*************:8096 from beat map.
2024-11-07 21:25:07.131 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 deregistering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"instanceIdGenerator":"simple","ip":"*************","ipDeleteTimeout":30000,"metadata":{},"port":8096,"weight":1.0}
2024-11-07 21:25:07.186 [SpringContextShutdownHook] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2024-11-07 21:25:07.191 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
2024-11-07 21:25:07.267 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2024-11-07 21:25:33.308 [main] INFO  c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2024-11-07 21:25:33.362 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2024-11-07 21:25:34.173 [main] INFO  com.alibaba.nacos.client.config.utils.JVMUtil - isMultiInstance:false
2024-11-07 21:25:34.220 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-oms-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms,DEFAULT_GROUP'}]
2024-11-07 21:25:34.319 [main] INFO  com.jsunicom.oms.BcpOmsApplication - The following profiles are active: dev
2024-11-07 21:25:38.693 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-11-07 21:25:38.696 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-11-07 21:25:38.875 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 165ms. Found 0 Redis repository interfaces.
2024-11-07 21:25:39.490 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=910dcfc4-df09-3e0d-b863-a1d487b6d31d
2024-11-07 21:25:40.042 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 21:25:40.184 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.sleuth.redis-org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties' of type [org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 21:25:40.764 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8096 (http)
2024-11-07 21:25:40.779 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8096"]
2024-11-07 21:25:40.780 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-11-07 21:25:40.781 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.29]
2024-11-07 21:25:41.024 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/oms-bcp] - Initializing Spring embedded WebApplicationContext
2024-11-07 21:25:41.024 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6628 ms
2024-11-07 21:25:42.443 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 21:25:42.553 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 21:25:42.554 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2024-11-07 21:25:42.555 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 21:25:47.852 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-11-07 21:25:49.770 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-11-07 21:25:50.095 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-07 21:25:50.103 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-07 21:25:50.765 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'taskScheduler'
2024-11-07 21:25:52.480 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 21:25:53.489 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 21:25:56.275 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8096"]
2024-11-07 21:25:56.365 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8096 (http) with context path '/oms-bcp'
2024-11-07 21:25:56.371 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: {"cluster":"DEFAULT","ip":"*************","metadata":{"preserved.register.source":"SPRING_CLOUD"},"period":5000,"port":8096,"scheduled":false,"serviceName":"DEFAULT_GROUP@@bcp-oms","stopped":false,"weight":1.0} to beat map.
2024-11-07 21:25:56.406 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8096,"weight":1.0}
2024-11-07 21:25:56.549 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bcp-oms *************:8096 register finished
2024-11-07 21:25:57.557 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 21:25:57.560 [main] INFO  com.jsunicom.oms.BcpOmsApplication - Started BcpOmsApplication in 28.802 seconds (JVM running for 31.405)
2024-11-07 21:25:58.139 [RMI TCP Connection(2)-127.0.0.1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/oms-bcp] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-11-07 21:25:58.140 [RMI TCP Connection(2)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-11-07 21:25:58.179 [RMI TCP Connection(2)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 39 ms
2024-11-07 21:25:58.448 [RMI TCP Connection(3)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-11-07 21:25:59.745 [RMI TCP Connection(3)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-11-07 21:25:59.812 [RMI TCP Connection(3)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2024-11-07 21:26:00.266 [RMI TCP Connection(3)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2024-11-07 21:26:00.516 [boundedElastic-1] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-11-07 21:26:00.542 [boundedElastic-1] INFO  io.lettuce.core.KqueueProvider - Starting with kqueue library
2024-11-07 21:26:12.815 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:26:12.815 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:26:12.817 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:26:22.272 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:26:22.273 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:26:22.339 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:26:22.340 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2F
2024-11-07 21:26:22.340 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:26:32.406 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:26:32.406 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:26:32.406 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:26:32.487 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:26:32.487 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:26:32.488 [http-nio-8096-exec-2] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:26:32.488 [http-nio-8096-exec-2] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2F
2024-11-07 21:26:32.488 [http-nio-8096-exec-2] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:26:42.187 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:26:42.188 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:26:42.188 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:26:42.281 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:26:42.282 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:26:42.285 [http-nio-8096-exec-3] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:26:42.286 [http-nio-8096-exec-3] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2F
2024-11-07 21:26:42.286 [http-nio-8096-exec-3] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:27:08.093 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:27:08.096 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:27:08.100 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:27:08.360 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:27:08.360 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:27:08.361 [http-nio-8096-exec-4] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:27:23.417 [http-nio-8096-exec-4] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2F
2024-11-07 21:27:26.114 [http-nio-8096-exec-4] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:28:39.871 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:28:39.873 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:28:39.873 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:28:39.962 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:28:39.965 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:28:39.972 [http-nio-8096-exec-5] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:28:42.401 [http-nio-8096-exec-5] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2F
2024-11-07 21:28:42.402 [http-nio-8096-exec-5] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:29:32.134 [com.alibaba.nacos.naming.beat.sender] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8096,"serviceName":"DEFAULT_GROUP@@bcp-oms","weight":1.0}
2024-11-07 21:33:55.998 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:33:56.010 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:33:56.011 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:33:56.176 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:33:56.177 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:33:56.193 [http-nio-8096-exec-6] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:33:56.194 [http-nio-8096-exec-6] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2F
2024-11-07 21:33:56.194 [http-nio-8096-exec-6] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:34:47.821 [com.alibaba.nacos.naming.beat.sender] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8096,"serviceName":"DEFAULT_GROUP@@bcp-oms","weight":1.0}
2024-11-07 21:35:17.013 [http-nio-8096-exec-6] INFO  c.j.oms.controller.OperationPermissionController - OperationPermissionController error :nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 30388ms.
### The error may exist in file [/Users/<USER>/workspace/sx_school/sx_school_backend/service/bcp-oms/target/classes/mapper/base/DictMapper.xml]
### The error may involve com.jsunicom.oms.mapper.base.DictDao.findByUK
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 30388ms.
2024-11-07 21:35:17.199 [http-nio-8096-exec-6] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:35:35.472 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 21:35:35.562 [lettuce-kqueueEventLoop-4-3] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:35:37.071 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:35:37.072 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/notice/list
2024-11-07 21:35:37.073 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:35:57.188 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:35:57.189 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:37:33.795 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:37:33.801 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:37:33.805 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:37:33.928 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:37:33.929 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:37:33.937 [http-nio-8096-exec-9] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:37:33.938 [http-nio-8096-exec-9] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2F
2024-11-07 21:37:33.939 [http-nio-8096-exec-9] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:41:14.765 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:41:14.768 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:41:14.769 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:41:14.890 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:41:14.890 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:41:14.905 [http-nio-8096-exec-10] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:41:14.906 [http-nio-8096-exec-10] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2F
2024-11-07 21:41:14.907 [http-nio-8096-exec-10] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:41:29.003 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:41:29.005 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:41:29.005 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:41:29.089 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:41:29.089 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:41:29.091 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:41:29.091 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2F
2024-11-07 21:41:29.092 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:41:54.694 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:41:54.695 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:41:54.695 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:41:54.817 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:41:54.818 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:41:54.820 [http-nio-8096-exec-2] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:41:54.820 [http-nio-8096-exec-2] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2F
2024-11-07 21:41:54.821 [http-nio-8096-exec-2] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:44:59.502 [main] INFO  c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config
2024-11-07 21:44:59.570 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2024-11-07 21:44:59.782 [main] INFO  com.alibaba.nacos.client.config.utils.JVMUtil - isMultiInstance:false
2024-11-07 21:44:59.809 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-bcp-oms-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-bcp-oms,DEFAULT_GROUP'}]
2024-11-07 21:44:59.863 [main] INFO  com.jsunicom.oms.BcpOmsApplication - The following profiles are active: dev
2024-11-07 21:45:04.703 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2024-11-07 21:45:04.706 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-11-07 21:45:04.890 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 168ms. Found 0 Redis repository interfaces.
2024-11-07 21:45:05.580 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=910dcfc4-df09-3e0d-b863-a1d487b6d31d
2024-11-07 21:45:06.047 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 21:45:06.165 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.sleuth.redis-org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties' of type [org.springframework.cloud.sleuth.instrument.redis.TraceRedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-11-07 21:45:06.758 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8096 (http)
2024-11-07 21:45:06.779 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8096"]
2024-11-07 21:45:06.780 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2024-11-07 21:45:06.781 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.29]
2024-11-07 21:45:07.173 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/oms-bcp] - Initializing Spring embedded WebApplicationContext
2024-11-07 21:45:07.174 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7285 ms
2024-11-07 21:45:10.105 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 21:45:10.292 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 21:45:10.295 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2024-11-07 21:45:10.296 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2024-11-07 21:45:17.224 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2024-11-07 21:45:19.141 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2024-11-07 21:45:19.463 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-07 21:45:19.469 [main] INFO  com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2024-11-07 21:45:19.965 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'taskScheduler'
2024-11-07 21:45:21.542 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 21:45:22.553 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 21:45:25.205 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8096"]
2024-11-07 21:45:25.243 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8096 (http) with context path '/oms-bcp'
2024-11-07 21:45:25.248 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: {"cluster":"DEFAULT","ip":"*************","metadata":{"preserved.register.source":"SPRING_CLOUD"},"period":5000,"port":8096,"scheduled":false,"serviceName":"DEFAULT_GROUP@@bcp-oms","stopped":false,"weight":1.0} to beat map.
2024-11-07 21:45:25.284 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8096,"weight":1.0}
2024-11-07 21:45:25.434 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bcp-oms *************:8096 register finished
2024-11-07 21:45:26.442 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2024-11-07 21:45:26.445 [main] INFO  com.jsunicom.oms.BcpOmsApplication - Started BcpOmsApplication in 31.162 seconds (JVM running for 33.569)
2024-11-07 21:45:27.017 [RMI TCP Connection(5)-127.0.0.1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/oms-bcp] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-11-07 21:45:27.018 [RMI TCP Connection(5)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2024-11-07 21:45:27.053 [RMI TCP Connection(5)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 35 ms
2024-11-07 21:45:27.189 [RMI TCP Connection(4)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2024-11-07 21:45:28.305 [RMI TCP Connection(4)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2024-11-07 21:45:28.370 [RMI TCP Connection(4)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2024-11-07 21:45:28.892 [RMI TCP Connection(4)-127.0.0.1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2024-11-07 21:45:29.184 [boundedElastic-1] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2024-11-07 21:45:29.212 [boundedElastic-1] INFO  io.lettuce.core.KqueueProvider - Starting with kqueue library
2024-11-07 21:47:33.837 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:47:33.837 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:47:33.839 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:47:34.091 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:47:34.092 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:47:34.160 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:47:34.161 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2F
2024-11-07 21:47:34.161 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:47:34.420 [http-nio-8096-exec-1] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:47:34.463 [http-nio-8096-exec-1] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --jsonnull
2024-11-07 21:47:34.728 [http-nio-8096-exec-1] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_manager_campus_bind
2024-11-07 21:47:34.767 [http-nio-8096-exec-1] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --jsonnull
2024-11-07 21:47:42.974 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:47:42.974 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:47:42.975 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:47:43.074 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:47:43.074 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:47:43.076 [http-nio-8096-exec-2] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:47:43.076 [http-nio-8096-exec-2] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2F
2024-11-07 21:47:43.076 [http-nio-8096-exec-2] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:47:52.124 [http-nio-8096-exec-2] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:47:52.192 [http-nio-8096-exec-2] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --json"{\"code\":\"switch_prohibit_access\",\"createBy\":\"Z000LJX1\",\"createTime\":1706148855000,\"id\":2368,\"kind\":\"switch\",\"kindDesc\":\"非校园内部人员禁止访问开关：0 关、1开\",\"name\":\"1\",\"orderBy\":1,\"status\":1,\"updateBy\":\"Z000LJX1\",\"updateTime\":1706149091000}"
2024-11-07 21:47:52.358 [http-nio-8096-exec-2] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_manager_campus_bind
2024-11-07 21:47:52.404 [http-nio-8096-exec-2] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --json"{\"code\":\"switch_manager_campus_bind\",\"createBy\":\"Z000LJX1\",\"createTime\":1706171667000,\"id\":2369,\"kind\":\"switch\",\"kindDesc\":\"是否校验校区经理绑定校区开关：0 关、1 开\",\"name\":\"1\",\"orderBy\":1,\"status\":1}"
2024-11-07 21:50:09.790 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:50:09.791 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:50:09.791 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:50:09.916 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:50:09.916 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:50:09.918 [http-nio-8096-exec-3] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:50:09.918 [http-nio-8096-exec-3] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2F
2024-11-07 21:50:09.918 [http-nio-8096-exec-3] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:50:09.964 [http-nio-8096-exec-3] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:50:10.005 [http-nio-8096-exec-3] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --json"{\"code\":\"switch_prohibit_access\",\"createBy\":\"Z000LJX1\",\"createTime\":1706148855000,\"id\":2368,\"kind\":\"switch\",\"kindDesc\":\"非校园内部人员禁止访问开关：0 关、1开\",\"name\":\"1\",\"orderBy\":1,\"status\":1,\"updateBy\":\"Z000LJX1\",\"updateTime\":1706149091000}"
2024-11-07 21:50:10.344 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:50:10.344 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/notice/list
2024-11-07 21:50:10.344 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:50:10.388 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:50:10.389 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:50:42.927 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:50:42.928 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:50:42.928 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:51:12.947 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:51:12.954 [http-nio-8096-exec-5] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:51:12.954 [http-nio-8096-exec-5] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2Fsys%2FbannerImg
2024-11-07 21:51:12.954 [http-nio-8096-exec-5] INFO  c.j.oms.controller.OperationPermissionController - staffNo:null
2024-11-07 21:51:13.718 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 21:51:13.739 [lettuce-kqueueEventLoop-4-3] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:51:16.039 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:51:16.039 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:51:16.040 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:51:22.434 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:51:22.434 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:51:22.434 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:51:23.499 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:51:23.499 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:51:23.499 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:51:24.346 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:51:24.346 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:51:24.346 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:51:32.965 [http-nio-8096-exec-5] INFO  c.j.oms.controller.OperationPermissionController - OperationPermissionController error :Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 20 second(s)
2024-11-07 21:51:32.966 [http-nio-8096-exec-5] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:51:38.710 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:51:38.710 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:51:38.710 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:51:46.047 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:51:46.048 [http-nio-8096-exec-6] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:51:46.048 [http-nio-8096-exec-6] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2Fsys%2FjobManager
2024-11-07 21:51:46.049 [http-nio-8096-exec-6] INFO  c.j.oms.controller.OperationPermissionController - staffNo:null
2024-11-07 21:51:52.440 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:51:52.442 [http-nio-8096-exec-7] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:51:52.442 [http-nio-8096-exec-7] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2Fauth%2Fmenu
2024-11-07 21:51:52.442 [http-nio-8096-exec-7] INFO  c.j.oms.controller.OperationPermissionController - staffNo:null
2024-11-07 21:51:53.181 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:51:53.181 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/banner/fetchDisplayTypeSelect.json
2024-11-07 21:51:53.182 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:51:53.504 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:51:53.509 [http-nio-8096-exec-8] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:51:53.510 [http-nio-8096-exec-8] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2Fauth%2Ftree
2024-11-07 21:51:53.510 [http-nio-8096-exec-8] INFO  c.j.oms.controller.OperationPermissionController - staffNo:null
2024-11-07 21:51:54.357 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:51:54.360 [http-nio-8096-exec-9] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:51:54.360 [http-nio-8096-exec-9] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2Fsys%2Fdict
2024-11-07 21:51:54.360 [http-nio-8096-exec-9] INFO  c.j.oms.controller.OperationPermissionController - staffNo:null
2024-11-07 21:52:06.051 [http-nio-8096-exec-6] INFO  c.j.oms.controller.OperationPermissionController - OperationPermissionController error :Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 20 second(s)
2024-11-07 21:52:06.051 [http-nio-8096-exec-6] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:52:06.425 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:52:06.426 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:52:06.426 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:52:08.718 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:52:08.719 [http-nio-8096-exec-10] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:52:08.720 [http-nio-8096-exec-10] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2Fsys%2Fdict
2024-11-07 21:52:08.720 [http-nio-8096-exec-10] INFO  c.j.oms.controller.OperationPermissionController - staffNo:null
2024-11-07 21:52:12.444 [http-nio-8096-exec-7] INFO  c.j.oms.controller.OperationPermissionController - OperationPermissionController error :Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 20 second(s)
2024-11-07 21:52:12.444 [http-nio-8096-exec-7] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:52:13.513 [http-nio-8096-exec-8] INFO  c.j.oms.controller.OperationPermissionController - OperationPermissionController error :Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 20 second(s)
2024-11-07 21:52:13.513 [http-nio-8096-exec-8] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:52:14.364 [http-nio-8096-exec-9] INFO  c.j.oms.controller.OperationPermissionController - OperationPermissionController error :Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 20 second(s)
2024-11-07 21:52:14.365 [http-nio-8096-exec-9] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:52:23.188 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:52:28.722 [http-nio-8096-exec-10] INFO  c.j.oms.controller.OperationPermissionController - OperationPermissionController error :Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 20 second(s)
2024-11-07 21:52:28.724 [http-nio-8096-exec-10] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:52:31.472 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:52:31.474 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:52:31.474 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:52:36.429 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:52:36.434 [http-nio-8096-exec-2] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:52:36.439 [http-nio-8096-exec-2] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2ForderInquiry
2024-11-07 21:52:36.441 [http-nio-8096-exec-2] INFO  c.j.oms.controller.OperationPermissionController - staffNo:null
2024-11-07 21:52:56.442 [http-nio-8096-exec-2] INFO  c.j.oms.controller.OperationPermissionController - OperationPermissionController error :Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 20 second(s)
2024-11-07 21:52:56.442 [http-nio-8096-exec-2] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:53:01.480 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:53:01.482 [http-nio-8096-exec-3] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:53:01.482 [http-nio-8096-exec-3] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2Fsys%2Fdict
2024-11-07 21:53:01.482 [http-nio-8096-exec-3] INFO  c.j.oms.controller.OperationPermissionController - staffNo:null
2024-11-07 21:53:08.434 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:53:08.434 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:53:08.434 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:53:21.486 [http-nio-8096-exec-3] INFO  c.j.oms.controller.OperationPermissionController - OperationPermissionController error :Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 20 second(s)
2024-11-07 21:53:21.487 [http-nio-8096-exec-3] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:53:26.605 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 21:53:26.613 [lettuce-kqueueEventLoop-4-4] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:27.722 [lettuce-kqueueEventLoop-4-5] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:28.820 [lettuce-kqueueEventLoop-4-6] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:29.913 [lettuce-kqueueEventLoop-4-7] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:31.112 [lettuce-kqueueEventLoop-4-8] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:32.206 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 21:53:32.217 [lettuce-kqueueEventLoop-4-1] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:33.311 [lettuce-kqueueEventLoop-4-2] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:34.407 [lettuce-kqueueEventLoop-4-3] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:35.515 [lettuce-kqueueEventLoop-4-4] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:36.652 [lettuce-kqueueEventLoop-4-5] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:37.808 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 21:53:37.834 [lettuce-kqueueEventLoop-4-6] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:38.437 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:53:38.439 [http-nio-8096-exec-4] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:53:38.439 [http-nio-8096-exec-4] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2Fsys%2Fdict
2024-11-07 21:53:38.440 [http-nio-8096-exec-4] INFO  c.j.oms.controller.OperationPermissionController - staffNo:null
2024-11-07 21:53:38.908 [lettuce-kqueueEventLoop-4-7] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:40.012 [lettuce-kqueueEventLoop-4-8] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:41.113 [lettuce-kqueueEventLoop-4-1] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:42.209 [lettuce-kqueueEventLoop-4-2] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:43.304 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 21:53:43.312 [lettuce-kqueueEventLoop-4-3] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:44.416 [lettuce-kqueueEventLoop-4-4] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:45.508 [lettuce-kqueueEventLoop-4-5] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:46.616 [lettuce-kqueueEventLoop-4-6] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:47.808 [lettuce-kqueueEventLoop-4-7] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:48.908 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 21:53:48.912 [lettuce-kqueueEventLoop-4-8] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:50.016 [lettuce-kqueueEventLoop-4-1] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:51.110 [lettuce-kqueueEventLoop-4-2] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:52.210 [lettuce-kqueueEventLoop-4-3] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:53.312 [lettuce-kqueueEventLoop-4-4] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:54.406 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 21:53:54.409 [lettuce-kqueueEventLoop-4-5] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:55.606 [lettuce-kqueueEventLoop-4-6] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:56.711 [lettuce-kqueueEventLoop-4-7] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:57.818 [lettuce-kqueueEventLoop-4-8] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:53:58.444 [http-nio-8096-exec-4] INFO  c.j.oms.controller.OperationPermissionController - OperationPermissionController error :Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 20 second(s)
2024-11-07 21:53:58.444 [http-nio-8096-exec-4] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:53:59.010 [lettuce-kqueueEventLoop-4-1] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:54:00.107 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 21:54:00.113 [lettuce-kqueueEventLoop-4-2] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:54:01.208 [lettuce-kqueueEventLoop-4-3] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:54:02.405 [lettuce-kqueueEventLoop-4-4] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:54:03.512 [lettuce-kqueueEventLoop-4-5] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:54:04.616 [lettuce-kqueueEventLoop-4-6] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:54:05.707 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 21:54:05.710 [lettuce-kqueueEventLoop-4-7] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:54:18.719 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:54:18.719 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operator/dict/condition
2024-11-07 21:54:18.719 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:54:48.723 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:54:48.746 [http-nio-8096-exec-5] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageNumber, newValues: 1
2024-11-07 21:54:48.747 [http-nio-8096-exec-5] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageSize, newValues: 20
2024-11-07 21:54:48.747 [http-nio-8096-exec-5] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: kind, newValues: 
2024-11-07 21:54:48.747 [http-nio-8096-exec-5] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: kindDesc, newValues: 
2024-11-07 21:54:48.747 [http-nio-8096-exec-5] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: code, newValues: 
2024-11-07 21:54:48.747 [http-nio-8096-exec-5] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: name, newValues: 
2024-11-07 21:54:48.747 [http-nio-8096-exec-5] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: orderBy, newValues: 
2024-11-07 21:54:48.785 [http-nio-8096-exec-5] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageNumber, newValues: 1
2024-11-07 21:54:48.787 [http-nio-8096-exec-5] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageSize, newValues: 20
2024-11-07 21:54:48.797 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.controller.DictMgrController - 分页查询字典信息,kind:,kindDesc:,code:,name:,createBy:null,updateBy:null,pageNumber:1,pageSize:20
2024-11-07 21:55:38.004 [com.alibaba.nacos.naming.beat.sender] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 78fe3dfc-fd29-4d3e-8200-52d9b014a563 registering service DEFAULT_GROUP@@bcp-oms with instance: {"clusterName":"DEFAULT","enabled":true,"ephemeral":true,"healthy":true,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ip":"*************","ipDeleteTimeout":30000,"metadata":{"preserved.register.source":"SPRING_CLOUD"},"port":8096,"serviceName":"DEFAULT_GROUP@@bcp-oms","weight":1.0}
2024-11-07 21:55:38.204 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 21:55:38.222 [lettuce-kqueueEventLoop-4-8] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:55:44.405 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog - Reconnecting, last destination was ************/************:16002
2024-11-07 21:55:44.426 [lettuce-kqueueEventLoop-4-1] INFO  io.lettuce.core.protocol.ReconnectionHandler - Reconnected to ************:16002
2024-11-07 21:55:47.633 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:55:47.633 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:55:47.633 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:55:47.735 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:55:47.735 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:55:47.742 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:55:47.742 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2F
2024-11-07 21:55:47.742 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:55:47.781 [http-nio-8096-exec-1] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:55:47.821 [http-nio-8096-exec-1] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --json"{\"code\":\"switch_prohibit_access\",\"createBy\":\"Z000LJX1\",\"createTime\":1706148855000,\"id\":2368,\"kind\":\"switch\",\"kindDesc\":\"非校园内部人员禁止访问开关：0 关、1开\",\"name\":\"1\",\"orderBy\":1,\"status\":1,\"updateBy\":\"Z000LJX1\",\"updateTime\":1706149091000}"
2024-11-07 21:55:48.050 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:55:48.051 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/notice/list
2024-11-07 21:55:48.051 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:55:48.096 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:55:48.096 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:55:51.994 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:55:51.995 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:55:51.995 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:55:52.076 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:55:52.076 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:55:52.077 [http-nio-8096-exec-7] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:55:52.077 [http-nio-8096-exec-7] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2Fsys%2Fdict
2024-11-07 21:55:52.077 [http-nio-8096-exec-7] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:55:52.114 [http-nio-8096-exec-7] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:55:52.152 [http-nio-8096-exec-7] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --json"{\"code\":\"switch_prohibit_access\",\"createBy\":\"Z000LJX1\",\"createTime\":1706148855000,\"id\":2368,\"kind\":\"switch\",\"kindDesc\":\"非校园内部人员禁止访问开关：0 关、1开\",\"name\":\"1\",\"orderBy\":1,\"status\":1,\"updateBy\":\"Z000LJX1\",\"updateTime\":1706149091000}"
2024-11-07 21:55:52.290 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:55:52.290 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operator/dict/condition
2024-11-07 21:55:52.290 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:55:52.332 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:55:52.332 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:55:52.333 [http-nio-8096-exec-8] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageNumber, newValues: 1
2024-11-07 21:55:52.333 [http-nio-8096-exec-8] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageSize, newValues: 20
2024-11-07 21:55:52.333 [http-nio-8096-exec-8] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: kind, newValues: 
2024-11-07 21:55:52.334 [http-nio-8096-exec-8] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: kindDesc, newValues: 
2024-11-07 21:55:52.334 [http-nio-8096-exec-8] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: code, newValues: 
2024-11-07 21:55:52.334 [http-nio-8096-exec-8] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: name, newValues: 
2024-11-07 21:55:52.334 [http-nio-8096-exec-8] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: orderBy, newValues: 
2024-11-07 21:55:52.335 [http-nio-8096-exec-8] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageNumber, newValues: 1
2024-11-07 21:55:52.335 [http-nio-8096-exec-8] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageSize, newValues: 20
2024-11-07 21:55:52.335 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.controller.DictMgrController - 分页查询字典信息,kind:,kindDesc:,code:,name:,createBy:null,updateBy:null,pageNumber:1,pageSize:20
2024-11-07 21:55:52.501 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.controller.DictMgrController - 查询字典信息成功,total:357
2024-11-07 21:55:56.100 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:55:56.100 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:55:56.100 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:55:56.181 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:55:56.181 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:55:56.182 [http-nio-8096-exec-9] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:55:56.186 [http-nio-8096-exec-9] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2Fsys%2Fjob
2024-11-07 21:55:56.186 [http-nio-8096-exec-9] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:55:56.227 [http-nio-8096-exec-9] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:55:56.266 [http-nio-8096-exec-9] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --json"{\"code\":\"switch_prohibit_access\",\"createBy\":\"Z000LJX1\",\"createTime\":1706148855000,\"id\":2368,\"kind\":\"switch\",\"kindDesc\":\"非校园内部人员禁止访问开关：0 关、1开\",\"name\":\"1\",\"orderBy\":1,\"status\":1,\"updateBy\":\"Z000LJX1\",\"updateTime\":1706149091000}"
2024-11-07 21:55:56.495 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:55:56.495 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operator/jobManager/getJobsDetailByPage.json
2024-11-07 21:55:56.496 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:55:56.496 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:55:56.496 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operator/jobManager/getJobsByPage.json
2024-11-07 21:55:56.496 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:55:56.543 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:55:56.543 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:55:56.576 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:55:56.577 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:56:06.487 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:56:06.487 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:56:06.487 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:56:06.576 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:56:06.576 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:56:06.577 [http-nio-8096-exec-3] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:56:06.578 [http-nio-8096-exec-3] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2Fsys%2FjobManager
2024-11-07 21:56:06.578 [http-nio-8096-exec-3] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:56:06.616 [http-nio-8096-exec-3] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:56:06.653 [http-nio-8096-exec-3] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --json"{\"code\":\"switch_prohibit_access\",\"createBy\":\"Z000LJX1\",\"createTime\":1706148855000,\"id\":2368,\"kind\":\"switch\",\"kindDesc\":\"非校园内部人员禁止访问开关：0 关、1开\",\"name\":\"1\",\"orderBy\":1,\"status\":1,\"updateBy\":\"Z000LJX1\",\"updateTime\":1706149091000}"
2024-11-07 21:56:06.789 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:56:06.789 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operator/jobManager/getJobsByPage.json
2024-11-07 21:56:06.789 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:56:06.831 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:56:06.839 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:56:13.779 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:56:13.779 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:56:13.779 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:56:13.865 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:56:13.865 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:56:13.866 [http-nio-8096-exec-5] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:56:13.866 [http-nio-8096-exec-5] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2Fsys%2FdataLog
2024-11-07 21:56:13.867 [http-nio-8096-exec-5] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:56:13.905 [http-nio-8096-exec-5] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:56:13.942 [http-nio-8096-exec-5] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --json"{\"code\":\"switch_prohibit_access\",\"createBy\":\"Z000LJX1\",\"createTime\":1706148855000,\"id\":2368,\"kind\":\"switch\",\"kindDesc\":\"非校园内部人员禁止访问开关：0 关、1开\",\"name\":\"1\",\"orderBy\":1,\"status\":1,\"updateBy\":\"Z000LJX1\",\"updateTime\":1706149091000}"
2024-11-07 21:56:14.096 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:56:14.096 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/dataMonitor/findDict.json
2024-11-07 21:56:14.096 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:56:14.144 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:56:14.144 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:56:14.148 [http-nio-8096-exec-1] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageNumber, newValues: 1
2024-11-07 21:56:14.148 [http-nio-8096-exec-1] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageSize, newValues: 20
2024-11-07 21:56:14.154 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.controller.DataMonitorController - 查询数据监控字典start... ...
2024-11-07 21:56:14.208 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.controller.DataMonitorController - 查询数据监控字典成功,total:0
2024-11-07 21:56:14.208 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.controller.DataMonitorController - 查询数据监控字典end... ...
2024-11-07 21:56:14.217 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:56:14.217 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/dataMonitor/find.json
2024-11-07 21:56:14.218 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:56:14.259 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:56:14.260 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:56:14.262 [http-nio-8096-exec-6] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageNumber, newValues: 1
2024-11-07 21:56:14.262 [http-nio-8096-exec-6] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageSize, newValues: 20
2024-11-07 21:56:14.262 [http-nio-8096-exec-6] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: operateBy, newValues: 
2024-11-07 21:56:14.262 [http-nio-8096-exec-6] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: monitorModules, newValues: 
2024-11-07 21:56:14.263 [http-nio-8096-exec-6] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: monitorType, newValues: 
2024-11-07 21:56:14.263 [http-nio-8096-exec-6] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: dataId, newValues: 
2024-11-07 21:56:14.268 [http-nio-8096-exec-6] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageNumber, newValues: 1
2024-11-07 21:56:14.269 [http-nio-8096-exec-6] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageSize, newValues: 20
2024-11-07 21:56:14.269 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.controller.DataMonitorController - 查询数据监控start... ...
2024-11-07 21:56:14.320 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.controller.DataMonitorController - 查询数据监控成功,total:0
2024-11-07 21:56:14.320 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.controller.DataMonitorController - 查询数据监控end... ...
2024-11-07 21:56:23.351 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:56:23.351 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:56:23.351 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:56:23.429 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:56:23.429 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:56:23.430 [http-nio-8096-exec-7] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:56:23.430 [http-nio-8096-exec-7] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2Fsys%2Fnotice%2Ftype
2024-11-07 21:56:23.430 [http-nio-8096-exec-7] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:56:23.468 [http-nio-8096-exec-7] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:56:23.511 [http-nio-8096-exec-7] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --json"{\"code\":\"switch_prohibit_access\",\"createBy\":\"Z000LJX1\",\"createTime\":1706148855000,\"id\":2368,\"kind\":\"switch\",\"kindDesc\":\"非校园内部人员禁止访问开关：0 关、1开\",\"name\":\"1\",\"orderBy\":1,\"status\":1,\"updateBy\":\"Z000LJX1\",\"updateTime\":1706149091000}"
2024-11-07 21:56:23.683 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:56:23.684 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/notice/type/list
2024-11-07 21:56:23.684 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:56:23.726 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:56:23.726 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:56:56.338 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:56:56.339 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:56:56.340 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:56:56.421 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:56:56.421 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:56:56.422 [http-nio-8096-exec-9] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:56:56.422 [http-nio-8096-exec-9] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2Fsys%2FredisManager
2024-11-07 21:56:56.422 [http-nio-8096-exec-9] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:56:56.460 [http-nio-8096-exec-9] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:56:56.497 [http-nio-8096-exec-9] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --json"{\"code\":\"switch_prohibit_access\",\"createBy\":\"Z000LJX1\",\"createTime\":1706148855000,\"id\":2368,\"kind\":\"switch\",\"kindDesc\":\"非校园内部人员禁止访问开关：0 关、1开\",\"name\":\"1\",\"orderBy\":1,\"status\":1,\"updateBy\":\"Z000LJX1\",\"updateTime\":1706149091000}"
2024-11-07 21:57:01.473 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:57:01.473 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:57:01.473 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:57:01.559 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:57:01.559 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:57:01.560 [http-nio-8096-exec-2] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:57:01.561 [http-nio-8096-exec-2] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2Fsys%2FMap
2024-11-07 21:57:01.562 [http-nio-8096-exec-2] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:57:01.602 [http-nio-8096-exec-2] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:57:01.642 [http-nio-8096-exec-2] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --json"{\"code\":\"switch_prohibit_access\",\"createBy\":\"Z000LJX1\",\"createTime\":1706148855000,\"id\":2368,\"kind\":\"switch\",\"kindDesc\":\"非校园内部人员禁止访问开关：0 关、1开\",\"name\":\"1\",\"orderBy\":1,\"status\":1,\"updateBy\":\"Z000LJX1\",\"updateTime\":1706149091000}"
2024-11-07 21:57:01.993 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:57:01.993 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/offline/offlineManager/getOfflineOrgInfo
2024-11-07 21:57:01.993 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:57:02.076 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:57:02.076 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:12.545 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:12.547 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:59:12.548 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:12.636 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:12.636 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:12.637 [http-nio-8096-exec-3] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:59:12.638 [http-nio-8096-exec-3] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2FyouthInnoCo%2FyouthWXLog
2024-11-07 21:59:12.638 [http-nio-8096-exec-3] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:59:12.677 [http-nio-8096-exec-3] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:59:12.716 [http-nio-8096-exec-3] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --json"{\"code\":\"switch_prohibit_access\",\"createBy\":\"Z000LJX1\",\"createTime\":1706148855000,\"id\":2368,\"kind\":\"switch\",\"kindDesc\":\"非校园内部人员禁止访问开关：0 关、1开\",\"name\":\"1\",\"orderBy\":1,\"status\":1,\"updateBy\":\"Z000LJX1\",\"updateTime\":1706149091000}"
2024-11-07 21:59:12.948 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:12.949 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/appletLoginLog/queryAppletLoginLogList
2024-11-07 21:59:12.949 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:12.950 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:12.950 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operator/getDictsByKindNew
2024-11-07 21:59:12.951 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:12.998 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:12.998 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:13.037 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:13.038 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:13.049 [http-nio-8096-exec-5] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: kind, newValues: menuRole
2024-11-07 21:59:13.076 [http-nio-8096-exec-4] INFO  c.jsunicom.oms.controller.AppletLoginLogController - 进入小程序用户登录日志查询接口 AppletLoginLogController.queryAppletLoginLogList loginLogDto:LoginLogDto(pageNumber=1, pageSize=10, roleType=, acctNo=, userName=, startDate=null, endDate=null)
2024-11-07 21:59:13.242 [http-nio-8096-exec-4] INFO  c.jsunicom.oms.controller.AppletLoginLogController - 进入小程序用户登录日志查询接口 AppletLoginLogController.queryAppletLoginLogList  success
2024-11-07 21:59:16.724 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:16.724 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:59:16.724 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:16.812 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:16.813 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:16.814 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:59:16.814 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2Fgoods%2Flist
2024-11-07 21:59:16.814 [http-nio-8096-exec-1] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:59:16.857 [http-nio-8096-exec-1] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:59:16.901 [http-nio-8096-exec-1] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --json"{\"code\":\"switch_prohibit_access\",\"createBy\":\"Z000LJX1\",\"createTime\":1706148855000,\"id\":2368,\"kind\":\"switch\",\"kindDesc\":\"非校园内部人员禁止访问开关：0 关、1开\",\"name\":\"1\",\"orderBy\":1,\"status\":1,\"updateBy\":\"Z000LJX1\",\"updateTime\":1706149091000}"
2024-11-07 21:59:17.188 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:17.197 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:17.198 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operator/querySwitchStateByCode
2024-11-07 21:59:17.199 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operator/list
2024-11-07 21:59:17.200 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:17.199 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:17.260 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:17.260 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:17.261 [http-nio-8096-exec-7] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: kind, newValues: bus_line
2024-11-07 21:59:17.265 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.controller.DictMgrController - 进入DictMgrController.getDict方法
2024-11-07 21:59:17.308 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.controller.DictMgrController - 调用DictMgrController.getDict方法  成功
2024-11-07 21:59:17.309 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:17.309 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:17.312 [http-nio-8096-exec-6] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: code, newValues: switch_school_export
2024-11-07 21:59:17.335 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.controller.DictMgrController - DictMgrController.querySwitchStateByCode：开关查询请求，code：switch_school_export
2024-11-07 21:59:17.435 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:17.436 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operator/list
2024-11-07 21:59:17.436 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:17.479 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:17.480 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:17.481 [http-nio-8096-exec-8] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: kind, newValues: goods_second_type
2024-11-07 21:59:17.481 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.controller.DictMgrController - DictMgrController.querySwitchStateByCode：开关查询结果，name：1
2024-11-07 21:59:17.482 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.controller.DictMgrController - 进入DictMgrController.getDict方法
2024-11-07 21:59:17.521 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.controller.DictMgrController - 调用DictMgrController.getDict方法  成功
2024-11-07 21:59:17.546 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:17.547 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operator/list
2024-11-07 21:59:17.548 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:17.590 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:17.590 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:17.591 [http-nio-8096-exec-9] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: kind, newValues: goods_terminal_second_type
2024-11-07 21:59:17.593 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.controller.DictMgrController - 进入DictMgrController.getDict方法
2024-11-07 21:59:17.631 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.controller.DictMgrController - 调用DictMgrController.getDict方法  成功
2024-11-07 21:59:17.845 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:17.845 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/org/department/getCity
2024-11-07 21:59:17.845 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:17.888 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:17.889 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:18.218 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:18.218 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/partner/findOrgMap
2024-11-07 21:59:18.218 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:18.218 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/goods/findByPage
2024-11-07 21:59:18.218 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:18.218 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:18.260 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:18.261 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:18.271 [http-nio-8096-exec-3] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: name, newValues: 
2024-11-07 21:59:18.271 [http-nio-8096-exec-3] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: code, newValues: 
2024-11-07 21:59:18.272 [http-nio-8096-exec-3] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: goodsType, newValues: 
2024-11-07 21:59:18.273 [http-nio-8096-exec-3] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: goodsArea, newValues: 
2024-11-07 21:59:18.273 [http-nio-8096-exec-3] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageNo, newValues: 1
2024-11-07 21:59:18.273 [http-nio-8096-exec-3] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageSize, newValues: 20
2024-11-07 21:59:18.273 [http-nio-8096-exec-3] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: state, newValues: 
2024-11-07 21:59:18.292 [http-nio-8096-exec-3] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageNo, newValues: 1
2024-11-07 21:59:18.293 [http-nio-8096-exec-3] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageSize, newValues: 20
2024-11-07 21:59:18.297 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:18.297 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:18.318 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.controller.GoodsController - 进入分页查询商品信息方法 GoodsController.findByPage
2024-11-07 21:59:18.399 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.controller.GoodsController - 调用分页查询商品信息方法 GoodsController.findByPage成功);
2024-11-07 21:59:18.410 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.service.impl.OrgInfoServiceImpl - getCodeNameMap return:{SxCU=陕西省本部, 0029=西安, root=中国联通陕西省分公司, 0919=铜川, 0916=汉中, 0917=宝鸡, 0914=商洛, 0915=安康, 0912=榆林, 0913=渭南, 0910=咸阳, 0911=延安}
2024-11-07 21:59:22.395 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:22.395 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:59:22.395 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:22.474 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:22.474 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:22.475 [http-nio-8096-exec-5] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:59:22.475 [http-nio-8096-exec-5] INFO  c.j.oms.controller.OperationPermissionController - 参数名: , 参数值: 
2024-11-07 21:59:22.475 [http-nio-8096-exec-5] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2Fgoods%2Fedit
2024-11-07 21:59:22.475 [http-nio-8096-exec-5] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:59:22.512 [http-nio-8096-exec-5] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:59:22.550 [http-nio-8096-exec-5] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --json"{\"code\":\"switch_prohibit_access\",\"createBy\":\"Z000LJX1\",\"createTime\":1706148855000,\"id\":2368,\"kind\":\"switch\",\"kindDesc\":\"非校园内部人员禁止访问开关：0 关、1开\",\"name\":\"1\",\"orderBy\":1,\"status\":1,\"updateBy\":\"Z000LJX1\",\"updateTime\":1706149091000}"
2024-11-07 21:59:27.756 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:27.756 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/org/department/getCity
2024-11-07 21:59:27.756 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:27.844 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:27.844 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:27.951 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:27.951 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operator/list
2024-11-07 21:59:27.952 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:27.996 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:27.996 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:27.997 [http-nio-8096-exec-1] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: kind, newValues: goods_second_type
2024-11-07 21:59:27.997 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.controller.DictMgrController - 进入DictMgrController.getDict方法
2024-11-07 21:59:28.037 [http-nio-8096-exec-1] INFO  com.jsunicom.oms.controller.DictMgrController - 调用DictMgrController.getDict方法  成功
2024-11-07 21:59:28.049 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:28.049 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operator/list
2024-11-07 21:59:28.049 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:28.094 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:28.094 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:28.095 [http-nio-8096-exec-7] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: kind, newValues: goods_terminal_second_type
2024-11-07 21:59:28.095 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.controller.DictMgrController - 进入DictMgrController.getDict方法
2024-11-07 21:59:28.134 [http-nio-8096-exec-7] INFO  com.jsunicom.oms.controller.DictMgrController - 调用DictMgrController.getDict方法  成功
2024-11-07 21:59:41.375 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:41.375 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operationPermission/checkLoginUser
2024-11-07 21:59:41.375 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:41.461 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:41.461 [http-nio-8096-exec-6] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:41.463 [http-nio-8096-exec-6] INFO  c.j.oms.controller.OperationPermissionController - 进入登录用户操作权限校验接口 OperationPermissionController.checkLoginUser
2024-11-07 21:59:41.463 [http-nio-8096-exec-6] INFO  c.j.oms.controller.OperationPermissionController - 参数名: , 参数值: 
2024-11-07 21:59:41.463 [http-nio-8096-exec-6] INFO  c.j.oms.controller.OperationPermissionController - 参数名: path, 参数值: %2Fgoods%2Flist
2024-11-07 21:59:41.463 [http-nio-8096-exec-6] INFO  c.j.oms.controller.OperationPermissionController - staffNo:test_province
2024-11-07 21:59:41.503 [http-nio-8096-exec-6] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --dictListKey==bcp.h_dict_switch  code:switch_prohibit_access
2024-11-07 21:59:41.546 [http-nio-8096-exec-6] INFO  c.j.oms.service.impl.DictFacadeOldServiceImpl - --json"{\"code\":\"switch_prohibit_access\",\"createBy\":\"Z000LJX1\",\"createTime\":1706148855000,\"id\":2368,\"kind\":\"switch\",\"kindDesc\":\"非校园内部人员禁止访问开关：0 关、1开\",\"name\":\"1\",\"orderBy\":1,\"status\":1,\"updateBy\":\"Z000LJX1\",\"updateTime\":1706149091000}"
2024-11-07 21:59:41.671 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:41.672 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:41.673 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operator/list
2024-11-07 21:59:41.673 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operator/querySwitchStateByCode
2024-11-07 21:59:41.674 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:41.674 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:41.715 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:41.715 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:41.718 [http-nio-8096-exec-8] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: code, newValues: switch_school_export
2024-11-07 21:59:41.719 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.controller.DictMgrController - DictMgrController.querySwitchStateByCode：开关查询请求，code：switch_school_export
2024-11-07 21:59:41.756 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:41.757 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:41.758 [http-nio-8096-exec-9] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: kind, newValues: bus_line
2024-11-07 21:59:41.759 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.controller.DictMgrController - 进入DictMgrController.getDict方法
2024-11-07 21:59:41.801 [http-nio-8096-exec-9] INFO  com.jsunicom.oms.controller.DictMgrController - 调用DictMgrController.getDict方法  成功
2024-11-07 21:59:41.823 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:41.823 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operator/list
2024-11-07 21:59:41.823 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:41.868 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:41.868 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:41.869 [http-nio-8096-exec-2] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: kind, newValues: goods_second_type
2024-11-07 21:59:41.869 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.controller.DictMgrController - 进入DictMgrController.getDict方法
2024-11-07 21:59:41.881 [http-nio-8096-exec-8] INFO  com.jsunicom.oms.controller.DictMgrController - DictMgrController.querySwitchStateByCode：开关查询结果，name：1
2024-11-07 21:59:41.906 [http-nio-8096-exec-2] INFO  com.jsunicom.oms.controller.DictMgrController - 调用DictMgrController.getDict方法  成功
2024-11-07 21:59:41.920 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:41.921 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/operator/list
2024-11-07 21:59:41.921 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:41.966 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:41.967 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:41.968 [http-nio-8096-exec-3] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: kind, newValues: goods_terminal_second_type
2024-11-07 21:59:41.969 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.controller.DictMgrController - 进入DictMgrController.getDict方法
2024-11-07 21:59:42.012 [http-nio-8096-exec-3] INFO  com.jsunicom.oms.controller.DictMgrController - 调用DictMgrController.getDict方法  成功
2024-11-07 21:59:42.027 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:42.027 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/org/department/getCity
2024-11-07 21:59:42.027 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:42.068 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:42.068 [http-nio-8096-exec-10] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:43.149 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:43.149 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/goods/findByPage
2024-11-07 21:59:43.150 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:43.151 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - 进入过滤器 start... ...
2024-11-07 21:59:43.151 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - /oms-bcp/partner/findOrgMap
2024-11-07 21:59:43.151 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - User-Info：eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=
2024-11-07 21:59:43.240 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:43.240 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:43.242 [http-nio-8096-exec-5] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: name, newValues: 
2024-11-07 21:59:43.242 [http-nio-8096-exec-5] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: code, newValues: 
2024-11-07 21:59:43.242 [http-nio-8096-exec-5] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: goodsType, newValues: 
2024-11-07 21:59:43.242 [http-nio-8096-exec-5] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: goodsArea, newValues: 
2024-11-07 21:59:43.242 [http-nio-8096-exec-5] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageNo, newValues: 1
2024-11-07 21:59:43.243 [http-nio-8096-exec-5] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageSize, newValues: 20
2024-11-07 21:59:43.243 [http-nio-8096-exec-5] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: state, newValues: 
2024-11-07 21:59:43.243 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.TokenAuthorFilter - token filter过滤ok!
2024-11-07 21:59:43.243 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.filter.XSSFilter - 进入XSS过滤器
2024-11-07 21:59:43.244 [http-nio-8096-exec-5] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageNo, newValues: 1
2024-11-07 21:59:43.245 [http-nio-8096-exec-5] INFO  c.jsunicom.oms.filter.XssHttpServletRequestWraper - name: pageSize, newValues: 20
2024-11-07 21:59:43.245 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.controller.GoodsController - 进入分页查询商品信息方法 GoodsController.findByPage
2024-11-07 21:59:43.289 [http-nio-8096-exec-4] INFO  com.jsunicom.oms.service.impl.OrgInfoServiceImpl - getCodeNameMap return:{SxCU=陕西省本部, 0029=西安, root=中国联通陕西省分公司, 0919=铜川, 0916=汉中, 0917=宝鸡, 0914=商洛, 0915=安康, 0912=榆林, 0913=渭南, 0910=咸阳, 0911=延安}
2024-11-07 21:59:43.292 [http-nio-8096-exec-5] INFO  com.jsunicom.oms.controller.GoodsController - 调用分页查询商品信息方法 GoodsController.findByPage成功);
