<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.tbp.mapper.TbpCheckRoleInfoMapper">
  <select id="findSalesManagerByPhone" resultType="com.jsunicom.tbp.entity.SalesManager">
    SELECT * FROM sales_manager WHERE phone = #{phone} AND state = '1'
  </select>

  <select id="findTouristInfos" resultType="com.jsunicom.tbp.entity.TouristWaiting">
    select * from tourist_waiting t where tourist_phone = #{touristPhone} and t.is_assign = '0'
  </select>

  <select id="findPartnerByPhone" resultType="com.jsunicom.tbp.entity.Partner">
    select * from partner t where t.acct_no = #{phone} and t.state = '1'
  </select>

  <select id="findMemberByPhone" resultType="com.jsunicom.tbp.entity.WoScYiMember">
    select * from WO_SC_YI_MEMBER t where t.acct_no = #{phone} and t.state = '1' and t.POSITION_TYPE = '1' and t.ROLE_TYPE in ('1','2')
  </select>

  <select id="findUserRoleInfos" resultType="com.jsunicom.tbp.entity.UserRoleInfo">
    select * from user_role_info t where t.serial_number = #{phone} and t.state = '0'
  </select>
  <select id="selectMenuList" resultType="java.lang.String">
    select `name`  from dict d where d.kind='menuList' and d.code in (
    <foreach collection="list" item="item" index="index" separator=",">
      #{item}
    </foreach>
    )
    order by d.code desc
  </select>
  <select id="selectChildMenuList" resultType="java.lang.String">
    select `name`  from dict d where d.kind='childMenuList' and d.code in (
    <foreach collection="list" item="item" index="index" separator=",">
      #{item}
    </foreach>
    )
    order by d.code desc
  </select>
  <select id="selectDictByRole" resultType="java.lang.String">
    select `name` from dict where code =#{code} and kind=#{kind}
  </select>
  <select id="selectSchoolIdByMerchantId" resultType="java.lang.Long">
    select id from wo_school_info where merchant_id=#{merchantId}
  </select>
  <select id="getYouthInnovateInfoBySocietyId" resultType="java.lang.String">
    select state from WO_SC_YOUTH_INNOVATE_BASE where APPROVAL_STATE= '1' and SOCIETY_ID = #{merchantId}
  </select>
  <select id="getPendingProcessCount" resultType="java.lang.Integer">
    select
        count(1) allCount
    from
        WO_SC_YI_WORK_ORDER a
    left join WO_SC_YI_WORK_RECORD b on
        a.ORDER_ID = b.ORDER_ID
        and b.STATUS_FLAG = 'R'
    where b.ACCEPT_ACCT_NO = #{phoneNumber}
  </select>
  <select id="getProcessedProcessCount" resultType="java.lang.Integer">
    SELECT COUNT(1) allCount
    FROM (
        SELECT b.ORDER_ID AS orderId, count(b.INST_ID) AS num
        FROM WO_SC_YI_WORK_ORDER a
            LEFT JOIN WO_SC_YI_WORK_RECORD b ON a.ORDER_ID = b.ORDER_ID
        WHERE b.COMPLETE_ACCT_NO = #{phoneNumber}
        GROUP BY b.ORDER_ID
        HAVING num > 0
    ) tab
  </select>
</mapper>
