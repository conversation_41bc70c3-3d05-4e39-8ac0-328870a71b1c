package com.jsunicom.api.service.impl;

import com.github.pagehelper.PageHelper;
import com.jsunicom.api.mapper.UserActionDictDao;
import com.jsunicom.api.po.UserActionDict;
import com.jsunicom.api.service.UserActionDictCache;
import com.jsunicom.api.service.UserActionDictFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-04-13-19:29
 */
@Slf4j
@Service
public class UserActionDictServiceImpl implements UserActionDictFacade {

    @Autowired
    private UserActionDictCache userActionDictCache;
    @Autowired
    private UserActionDictDao userActionDictDao;
    @Override
    public UserActionDict getUserActionDictByActionId(String actionId) {
        return userActionDictCache.getActionDictByActionId(actionId);
        //return dictDao.getDictByKind(kind);
    }
    @Override
    public ArrayList<UserActionDict> find() {
        HashMap<String, Object> paramMap = new HashMap();
        ArrayList<UserActionDict> pageList = userActionDictDao.findByPage(paramMap);
        paramMap.clear();
        return pageList;
    }
}
