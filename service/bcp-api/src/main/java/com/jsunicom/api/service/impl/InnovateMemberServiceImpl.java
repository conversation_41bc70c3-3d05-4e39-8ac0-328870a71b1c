package com.jsunicom.api.service.impl;

import com.jsunicom.api.mapper.WoScYiMemberMapper;
import com.jsunicom.api.po.WoScYiMember;
import com.jsunicom.api.po.WoScYiMemberExample;
import com.jsunicom.api.service.InnovateMemberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @BelongsProject: jspop_backend
 * @BelongsPackage: com.jsunicom.api.service.impl
 * @ClassName: InnovateMemberServiceImpl
 * @Author: zhaowang
 * @CreateTime: 2023-07-17  11:56
 * @Description: TODO
 * @Version: 1.0
 */
@Slf4j
@Service
public class InnovateMemberServiceImpl implements InnovateMemberService {
    @Autowired
    private WoScYiMemberMapper memberMapper;

    @Override
    public WoScYiMember findById(Long id) {
        return memberMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<WoScYiMember> findByAcctNo(String acctNo) {
        WoScYiMemberExample example = new WoScYiMemberExample();
        WoScYiMemberExample.Criteria criteria = example.createCriteria();
        criteria.andAcctNoEqualTo(acctNo);
        criteria.andStateEqualTo("1");
        return memberMapper.selectByExample(example);
    }
}
