package com.jsunicom.api.entity.school;


import com.jsunicom.api.model.base.BasicModel;

import java.util.Date;

public class SchoolInfo extends BasicModel {
    private long id;

    private String schoolName;

    private Long merchantId;

    private String loginTitle;

    private String loginTip;

    private String loginLogo;

    private String bgImg;

    private String wlcmImg;

    private String wlcmLogo;

    private Long schoolManagerId;

    private String loginType;

    private Date createTime;

    private Long createBy;

    private Date updateTime;

    private Long updateBy;

    private String wlcmIntroduce;

    private String benefitInfo;

    /**
     * 集中生产工号
     */
    private String prodOperId;

    /**
     * 号池渠道编码
     */
    private String develChannel;

    /**
     * 介绍页标题
     */
    private String wlcmTitle;
    /**
     * 立体分层图片第一张
     */
    private String solidLayeringA;
    /**
     * 立体分层图片第二张
     */
    private String solidLayeringB;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSchoolName() {
        return schoolName;
    }

    public void setSchoolName(String schoolName) {
        this.schoolName = schoolName == null ? null : schoolName.trim();
    }

    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    public String getLoginTitle() {
        return loginTitle;
    }

    public void setLoginTitle(String loginTitle) {
        this.loginTitle = loginTitle == null ? null : loginTitle.trim();
    }

    public String getLoginTip() {
        return loginTip;
    }

    public void setLoginTip(String loginTip) {
        this.loginTip = loginTip == null ? null : loginTip.trim();
    }

    public String getLoginLogo() {
        return loginLogo;
    }

    public void setLoginLogo(String loginLogo) {
        this.loginLogo = loginLogo == null ? null : loginLogo.trim();
    }

    public String getBgImg() {
        return bgImg;
    }

    public void setBgImg(String bgImg) {
        this.bgImg = bgImg == null ? null : bgImg.trim();
    }

    public String getWlcmImg() {
        return wlcmImg;
    }

    public void setWlcmImg(String wlcmImg) {
        this.wlcmImg = wlcmImg == null ? null : wlcmImg.trim();
    }

    public String getWlcmLogo() {
        return wlcmLogo;
    }

    public void setWlcmLogo(String wlcmLogo) {
        this.wlcmLogo = wlcmLogo == null ? null : wlcmLogo.trim();
    }

    public Long getSchoolManagerId() {
        return schoolManagerId;
    }

    public void setSchoolManagerId(Long schoolManagerId) {
        this.schoolManagerId = schoolManagerId;
    }

    public String getLoginType() {
        return loginType;
    }

    public void setLoginType(String loginType) {
        this.loginType = loginType == null ? null : loginType.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public String getWlcmIntroduce() {
        return wlcmIntroduce;
    }

    public void setWlcmIntroduce(String wlcmIntroduce) {
        this.wlcmIntroduce = wlcmIntroduce == null ? null : wlcmIntroduce.trim();
    }

    public String getBenefitInfo() {
        return benefitInfo;
    }

    public void setBenefitInfo(String benefitInfo) {
        this.benefitInfo = benefitInfo;
    }

    public String getProdOperId() {
        return prodOperId;
    }

    public void setProdOperId(String prodOperId) {
        this.prodOperId = prodOperId;
    }

    public String getDevelChannel() {
        return develChannel;
    }

    public void setDevelChannel(String develChannel) {
        this.develChannel = develChannel;
    }

    public String getWlcmTitle() {
        return wlcmTitle;
    }

    public void setWlcmTitle(String wlcmTitle) {
        this.wlcmTitle = wlcmTitle;
    }

    public String getSolidLayeringA() {
        return solidLayeringA;
    }

    public void setSolidLayeringA(String solidLayeringA) {
        this.solidLayeringA = solidLayeringA;
    }

    public String getSolidLayeringB() {
        return solidLayeringB;
    }

    public void setSolidLayeringB(String solidLayeringB) {
        this.solidLayeringB = solidLayeringB;
    }
}
