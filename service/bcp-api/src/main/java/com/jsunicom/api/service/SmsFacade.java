package com.jsunicom.api.service;

import com.jsunicom.common.core.util.Result;

import java.util.List;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.api.service.impl.SmsService
 * @ClassName: SmsService
 * @Author: <PERSON><PERSON><PERSON>
 * @CreateTime: 2023-04-10  10:31
 * @Description: TODO
 * @Version: 1.0
 */
public interface SmsFacade {
    Result  sendCode(String templateId,String mobile);
    public void checkCode(String mobile,String code);

    void sendRemindMsg(List<String> phoneNoList);
}
