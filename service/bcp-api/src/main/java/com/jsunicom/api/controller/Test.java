package com.jsunicom.api.controller;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.jsunicom.api.common.Constants;
import com.jsunicom.api.common.RedisKeys;
import com.jsunicom.api.entity.partner.Partner;
import com.jsunicom.api.po.WoScYiMember;
import com.jsunicom.api.utils.RedisUtil;
import com.jsunicom.common.core.util.Result;
import com.jsunicom.common.jslt.identity.dto.ManagerInfoDto;
import com.jsunicom.common.jslt.identity.dto.MemberInfoDto;
import com.jsunicom.common.jslt.identity.service.ManagerAndMemberInfoSyncService;
import com.jsunicom.common.sms.common.MessageDto;
import com.jsunicom.common.sms.sender.MessageSender;
import lombok.extern.slf4j.Slf4j;
import netscape.javascript.JSObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-03-14-14:46
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class Test extends BaseController{
    @Autowired
    MessageSender messageSender;
    @Autowired
    private RedisUtil redisUtils;
    @Autowired
    private ManagerAndMemberInfoSyncService managerAndMemberInfoSyncService;

    @GetMapping("/sendSms")
    public Result sendSms(){
        MessageDto messageDto = new MessageDto();
        messageDto.setPhoneNumber("13011228867");
        messageDto.setMessage("test");
        messageDto.setTemplateId("");
        return messageSender.sendMessage(messageDto);
    }

    @GetMapping("/hdel")
    public String updateRedisVal(String kind, String code){
        String dictListKey = RedisKeys.keyForDict(kind);
        try {
            redisUtils.hdel(dictListKey,code);
            return "success";
        }catch (Exception e){
            return e.getMessage();
        }
    }

    @GetMapping("/hdelByKind")
    public String hdelByKind(String kind){
        String dictListKey = RedisKeys.keyForDict(kind);
        try {
            redisUtils.del(dictListKey);
            return "success";
        }catch (Exception e){
            return e.getMessage();
        }
    }

    @GetMapping("/getRoleInfoDemo")
    public String getRoleInfoDemo(HttpServletRequest request){
        try {
            //step1 判断是校区经理还是青创社成员，1 校区经理，2 青创社成员
            String memberFlag = getMemberFlag(request);
            //step2 根据memberFlag值获取登录用户信息
            if (memberFlag.equals(Constants.MEMBER_FLAG_V1)){ //校区经理
                Partner partner = getCurrentUser(request);
                log.info(JSONObject.toJSONString(partner));
            }
            return "success";
        }catch (Exception e){
            return e.getMessage();
        }
    }

//    @PostMapping("/syncManagerInfoTest")
//    public Boolean syncManagerInfoTest(@RequestBody ManagerInfoDto managerInfoDto){
//        log.info("入参：{}",JSONObject.toJSONString(managerInfoDto));
//        return managerAndMemberInfoSyncService.managerInfoSync(managerInfoDto);
//    }
//
//    @PostMapping("/syncMemberInfoTest")
//    public Boolean syncMemberInfoTest(@RequestBody MemberInfoDto memberInfoDto){
//        log.info("入参：{}",JSONObject.toJSONString(memberInfoDto));
//        return managerAndMemberInfoSyncService.memberInfoSync(memberInfoDto);
//    }
}
