package com.jsunicom.api.service.impl;

import com.jsunicom.api.mapper.CampusCollegeMapper;
import com.jsunicom.api.po.WoSchoolCampusCollege;
import com.jsunicom.api.service.CampusCollegeFacade;

import javax.annotation.Resource;

/**
 * Project:CampusCollegeServiceImpl
 * Author:lilj
 * Date:2024/12/7
 * Description:
 */
public class CampusCollegeServiceImpl implements CampusCollegeFacade {

    @Resource
    private CampusCollegeMapper campusCollegeMapper;

    @Override
    public WoSchoolCampusCollege queryInfo(Long collegeId) {
        return campusCollegeMapper.query(collegeId);
    }
}
