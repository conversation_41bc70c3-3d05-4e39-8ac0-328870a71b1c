package com.jsunicom.api.model;

import com.jsunicom.api.entity.goods.WoScCreativeItemManagement;
import com.jsunicom.api.model.base.BasicModel;
import lombok.Data;

import java.util.List;

/**
 * Created by zhangtt131 on 2018/12/18.
 */
@Data
public class WoSchoolGoods extends BasicModel {
    private Long id;
    private Long schoolId;
    private Long goodsId;
    private String deliveryType;
    private String goodsPrice;
    private String goodsPicUrl;
    private String goodsName;
    private String selectFlag;
    private String goodsPostagePic;
    /**
     * 号池渠道编码
     */
    private String develChannel;
    /**
     * 首月生效方式
     */
    private String effectiveMethod;
    /**
     * 文创用品ID,多个以，分隔。
     */
    private String creativeItemId;
    /**
     * 最大选择数量
     */
    private Integer maxSelect;

    private List<WoScCreativeItemManagement> creativeItemManagements;
}
