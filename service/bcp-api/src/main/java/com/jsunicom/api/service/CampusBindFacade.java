package com.jsunicom.api.service;

import com.jsunicom.api.entity.Campus.CampusBind;

import java.util.List;

public interface CampusBindFacade {
    void save(CampusBind campusBind);
    /**
     * 根据团队mId 查询 校区与团队绑定关系
     *
     * @param mId
     * @return
     */
    CampusBind queryByMId(Long mId);

    /**
     *根据校区cId，查询 校区与经理绑定关系
     * @param cId
     * @return
     */
    CampusBind queryByCId(Long cId);

    /**
     *
     * @param campusBind
     */
    void removeBy(CampusBind campusBind);

    public List<CampusBind> queryListByCondition(Long mId);
}
