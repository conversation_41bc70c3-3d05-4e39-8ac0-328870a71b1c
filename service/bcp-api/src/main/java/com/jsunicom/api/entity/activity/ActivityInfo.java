package com.jsunicom.api.entity.activity;

import com.jsunicom.api.po.WoScYiActivity;
import lombok.Data;

/**
 * @BelongsProject: sx_school_backend
 * @BelongsPackage: com.jsunicom.api.entity
 * @ClassName: activity
 * @Author: z<PERSON>wang
 * @CreateTime: 2023-07-19  19:45
 * @Description: TODO
 * @Version: 1.0
 */
@Data
public class ActivityInfo extends WoScYiActivity {
    private String dealContent;

    private String photoUrl1;

    private String photoUrl2;

    private String photoUrl3;

    private String flowId;

    private String flowName;

    private String orderType;
}
