package com.jsunicom.api.mapper;

import com.jsunicom.api.po.MerchantInfo;
import com.jsunicom.api.po.MerchantInfoExample;
import java.util.List;

import com.jsunicom.api.po.MerchantListInfo;
import org.apache.ibatis.annotations.Param;

public interface MerchantInfoMapper {
    long countByExample(MerchantInfoExample example);

    int deleteByExample(MerchantInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(MerchantInfo record);

    int insertSelective(MerchantInfo record);

    List<MerchantInfo> selectByExample(MerchantInfoExample example);

    MerchantInfo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MerchantInfo record, @Param("example") MerchantInfoExample example);

    int updateByExample(@Param("record") MerchantInfo record, @Param("example") MerchantInfoExample example);

    int updateByPrimaryKeySelective(MerchantInfo record);

    int updateByPrimaryKey(MerchantInfo record);

    List<MerchantListInfo>  selectMerchantList(Long societyId);

    List<MerchantListInfo>  selectMerchantInfoList(Long societyId);

}