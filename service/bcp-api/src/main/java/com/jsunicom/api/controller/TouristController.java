package com.jsunicom.api.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jsunicom.api.common.ApiResultEntity;
import com.jsunicom.api.common.Constants;
import com.jsunicom.api.common.annotation.DecryptApi;
import com.jsunicom.api.common.annotation.EncryptApi;
import com.jsunicom.api.entity.Campus.Campus;
import com.jsunicom.api.entity.Campus.CampusBind;
import com.jsunicom.api.entity.partner.Partner;
import com.jsunicom.api.entity.tourist.TouristWaiting;
import com.jsunicom.api.po.PartnerInfo;
import com.jsunicom.api.po.SchoolCampusCollege;
import com.jsunicom.api.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 描述 游客
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/26 11:49:49
 */
@Slf4j
@RestController
@RequestMapping(value = "/tourist")
public class TouristController extends BaseController{

    @Autowired
    private TouristWaitingService touristWaitingService;
    @Autowired
    private PartnerInfoFacade partnerInfoFacade;
    @Autowired
    private CheckInfoService checkInfoService;
    @Autowired
    private SmsFacade smsService;
    @Autowired
    private CampusFacade campusService;
    @Autowired
    private WoSchoolFacade woSchoolFacade;

    @Autowired
    private PartnerFacade partnerFacade;
    @Autowired
    private SchoolCampusCollegeFacade schoolCampusCollegeFacade;

    /**
     * 描述
     * <AUTHOR>
     * @date 2025/3/26
     * @Return 查询当前电话号码绑定的校园
     */
    @ResponseBody
    @RequestMapping(value = "/getCampusListByNbr", method = RequestMethod.POST)
    @DecryptApi
    @EncryptApi
    public ApiResultEntity getCampusListByNbr(@RequestBody JSONObject jsonObject) {
        String mblNbr = jsonObject.getString("mbr");
        Partner partner = partnerFacade.findByAcctNo(mblNbr);
        String msg = "";
        boolean isSchoolManager = woSchoolFacade.isSchoolManager(mblNbr);
        Set<Campus> campusList = Sets.newTreeSet(new Comparator<Campus>() {
            @Override
            public int compare(Campus o1, Campus o2) {
                return o2.getCampusId().compareTo(o1.getCampusId());
            }
        });

        HashMap<String, Object> dataMap = Maps.newHashMap();

        //商家类型合伙人，查询归属商店
        if (Constants.PARTNER_TYPE_CHAINMERCHANT.equals(partner.getPartnerType())) {
            // 查询团队所属校区
            long societyId =  Long.parseLong(partner.getSocietyId());
            campusList.addAll(campusService.getCampusListBySz(societyId));
            msg = "当前登陆人[" + mblNbr + "]是商家类型合伙人，查询归属校区";

        }

        // 查询商家
        if (isSchoolManager) {
            CampusBind campusBind = new CampusBind();
            campusBind.setBindId(partner.getId());
            campusList.addAll(campusService.getCampusList(campusBind));
            msg += ",当前登陆人[" + mblNbr + "]是校区经理，查询管辖范围的商家";
        }

        log.info(msg);

        dataMap.put("campusList", campusList);
        dataMap.put("partnerId", partner.getId());
        return ApiResultEntity.SUCCESS(msg, dataMap);
    }
    /**
     * 描述  查询院系
     * <AUTHOR>
     * @date 2025/3/26
     */
    @ResponseBody
    @RequestMapping(value = "/getCollegeListByCampusId", method = RequestMethod.POST)
    @DecryptApi
    @EncryptApi
    public ApiResultEntity getCollegeListByCampusId(@RequestBody JSONObject jsonObject) {
        HashMap<String, Object> dataMap = Maps.newHashMap();
        Long campusId = jsonObject.getLong("campusId");
        JSONArray jsonArray = new JSONArray();
        List<SchoolCampusCollege> collegeList = schoolCampusCollegeFacade.queryInfoList(campusId);
        for (SchoolCampusCollege schoolCampusCollege : collegeList){
            String collegeName = schoolCampusCollege.getCollegeName();
            long collegeId = schoolCampusCollege.getCollegeId();
            JSONObject object = new JSONObject();
            object.put("collegeName", collegeName);
            object.put("collegeId", collegeId);
            jsonArray.add(object);
        }
        dataMap.put("collegeList", jsonArray);
        return ApiResultEntity.SUCCESS("查询成功", dataMap);
    }

    /**
     * 描述  创建游客
     * <AUTHOR>
     * @date 2025/3/26
     */
    @RequestMapping(value = "/createTourist", method = RequestMethod.POST, name = "创建游客")
    @DecryptApi
    @EncryptApi
    public ApiResultEntity createTourist(@RequestBody HashMap hashMap, HttpServletRequest request){

        log.info("进入createTourist方法："+ JSONObject.toJSONString(hashMap));
        String error = "";
        try {

            if(!hashMap.containsKey("touristCertNo") || !hashMap.containsKey("createBy") || !hashMap.containsKey("mblNbr")
                    || !hashMap.containsKey("touristName") ||  !hashMap.containsKey("campusName") ||  !hashMap.containsKey("collegeName")
                    || !hashMap.containsKey("campusId") ||  !hashMap.containsKey("collegeId")
                    || !hashMap.containsKey("inviteMblNbr")){
                error = "缺少必传字段";
                return  getErrorEntity(error);
            }

            // a)手机验证码校验：校验验证码是否正确。
            String mobile = hashMap.get("mblNbr").toString();
            String verificationCode = hashMap.get("verificationCode").toString();
            //目前因不能异网发送短信,暂时注释验证码校验 todo
//            smsService.checkCode(mobile, verificationCode);

            // 国政通校验：校验姓名与证件号是否一致
            hashMap.put("partnerCertNo",hashMap.get("touristCertNo")+"");
            hashMap.put("partnerName",hashMap.get("touristName")+"");
            boolean gztTag = checkInfoService.checkGztCode(hashMap);
            if(!gztTag){
                return ApiResultEntity.FAILURE("身份信息校验失败");
            }

            PartnerInfo partnerRecord = JSONObject.parseObject(JSONObject.toJSONString(hashMap), PartnerInfo.class);
            // d)校验注册人员信息是否在表中待审核、已审核状态存在，如果已存在给出提示
            PartnerInfo record = new PartnerInfo();
            record.setMblNbr(partnerRecord.getMblNbr());
            record.setState("0|1");
            record.setPartnerCertNo(partnerRecord.getPartnerCertNo());
            long partnerCount = partnerInfoFacade.countOrByExample(record);
            if(partnerCount>0){
                return getErrorEntity("该用户已存在");
            }
            //加入待选池表
            List<TouristWaiting> touristCertNo = touristWaitingService.selectTouristWaitingByCertNo(ObjectUtil.defaultIfNull(hashMap.get("touristCertNo") + "", ""));
            if(touristCertNo.size()>0){
                return getErrorEntity("该用户已存在");
            }
            TouristWaiting touristWaiting = new TouristWaiting();
            touristWaiting.setTouristName(ObjectUtil.defaultIfNull(hashMap.get("touristName")+"",""));
            touristWaiting.setTouristPhone(ObjectUtil.defaultIfNull(hashMap.get("mblNbr")+"",""));
            touristWaiting.setTouristCertId(ObjectUtil.defaultIfNull(hashMap.get("touristCertNo")+"",""));
            touristWaiting.setInvitePhoneNumber(ObjectUtil.defaultIfNull(hashMap.get("inviteMblNbr")+"",""));
            touristWaiting.setCampusId(Integer.parseInt(ObjectUtil.defaultIfNull(hashMap.get("campusId")+"","0")));
            touristWaiting.setCampusName(ObjectUtil.defaultIfNull(hashMap.get("campusName")+"",""));
            touristWaiting.setCollegeId(Integer.parseInt(ObjectUtil.defaultIfNull(hashMap.get("collegeId")+"","0")));
            touristWaiting.setCollegeName(ObjectUtil.defaultIfNull(hashMap.get("collegeName")+"",""));
            touristWaiting.setCreateBy(ObjectUtil.defaultIfNull(hashMap.get("createBy")+"",""));
            touristWaitingService.insertTouristWaiting(touristWaiting);

        }catch (Exception e){
            log.error("createTourist方法异常："+e.getMessage());
            error = "游客邀请异常";
            return  getErrorEntity("游客邀请异常"+e.getMessage());
        }

        return getSuccessEntity();

    }
    /**
     * 描述  获取当前登录客户经理
     * <AUTHOR>
     * @date 2025/3/26
     */
    @ResponseBody
    @RequestMapping(value = "/getLoginPartner", method = RequestMethod.GET)
    @DecryptApi
    @EncryptApi
    public ApiResultEntity getLoginPartner(HttpServletRequest request) {
        Partner partner = getCurrentUser(request);
        HashMap<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("partner", partner);
        return ApiResultEntity.SUCCESS("查询成功", dataMap);
    }
}
