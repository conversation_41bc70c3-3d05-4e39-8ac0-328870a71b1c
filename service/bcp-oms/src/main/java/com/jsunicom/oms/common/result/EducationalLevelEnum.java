package com.jsunicom.oms.common.result;

public enum EducationalLevelEnum {

    //定义返回值内容
    POSTGRADUATE("0","本科/专科"),
    UNDERGRADUATE("1","研究生")
    ;
    private String code;
    private String desc;

    EducationalLevelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getrespCode() {
        return code;
    }

    public String getrespDesc() {
        return desc;
    }
}
