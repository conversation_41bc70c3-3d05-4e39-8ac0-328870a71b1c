package com.jsunicom.oms.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jsunicom.oms.dto.user.UserInfoDto;
import com.jsunicom.oms.entity.TagDto;
import com.jsunicom.oms.model.partner.Partner;
import com.jsunicom.oms.po.WoScYiMember;
import com.jsunicom.oms.service.DataMonitorService;
import com.jsunicom.oms.service.PartnerFacade;
import com.jsunicom.oms.service.QywxFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-03-31-14:10
 */
@RestController
@RequestMapping(value = "/qywx")
@Slf4j
public class QywxController {

    @Autowired
    private QywxFacade qywxService;
    @Autowired
    PartnerFacade partnerService;
    @Autowired
    private DataMonitorService dataMonitorService;

    @RequestMapping(value = "/getQywxSign", method = RequestMethod.GET, name = "查询企业微信成员标签信息")
    public JSONObject getQywxSign() throws Exception
    {
        log.info("查询企业微信成员标签信息：QywxController--getQywxSign开始");
        JSONObject result = new JSONObject();

        //TODO appid 对应的api模块接口待定
//        result = qywxService.getQywxSign();
        log.info("查询企业微信成员标签信息,没筛选之前的数据:{}",result);
        JSONArray jsonArray = result.getJSONArray("SignList");
        for(int i=jsonArray.size()-1;i>=0;i--){
            JSONObject jsonObject=jsonArray.getJSONObject(i);
            String tagName=jsonObject.getString("tagName");
            boolean start= tagName.startsWith("系统_");
            if(!start){
                jsonArray.remove(i);
            }
        }
        result.put("SignList",jsonArray);
        log.info("查询企业微信成员标签信息：QywxController--getQywxSign结束,筛选之后的数据 result={}",result);
        return result;
    }
    @RequestMapping(value = "/qywxQuery", method = RequestMethod.GET, name = "查询企业微信成员信息")
    public JSONObject selectUser(String mobile, HttpServletRequest request) throws Exception
    {
        JSONObject result = new JSONObject();
        String orgCode = request.getHeader("areaCode");
        if(StringUtils.isNotEmpty(mobile)  ) {
            Partner partner=partnerService.getPartnerByMblNbr(mobile);
            if(partner!=null){
                if(!"root".equals(orgCode) && StringUtils.isNotEmpty(orgCode) && !orgCode.equals(partner.getOrgCode())){
                    result.put("ret", "1");
                    result.put("errcode", '1');
                    result.put("errMsg", "你无权限查询该合伙人信息!");
                    return result;
                }
                result = qywxService.selectUser(mobile);
                //关联查询青创社成员信息，是否有绑定发展人
                String develId = "";
                List<WoScYiMember> memberList = partnerService.getPartnerDevelIdByMblNbr(mobile);
                if (memberList.size()==1 && memberList.get(0).getDevelId()!=null){
                    develId = memberList.get(0).getDevelId();
                }
                result.put("develId",develId);
            }else{
                result.put("ret", "1");
                result.put("errcode", '1');
                result.put("errMsg", "该合伙人在展业库中不存在");
                return result;
            }
        }
        return result;
    }
    @RequestMapping(value = "/addSign", method = RequestMethod.GET, name = "增加成员标签")
    public JSONObject addSign(String userId,long tagId,String operateFlag,HttpServletRequest request) throws Exception
    {
        log.info("增加成员标签：QywxController--addSign开始");

        JSONObject result = new JSONObject();
        String orgCode = request.getHeader("areaCode");
        Partner partner=partnerService.getPartnerByMblNbr(userId);
        if(partner!=null){
            if(!"root".equals(orgCode) && StringUtils.isNotEmpty(orgCode) && !orgCode.equals(partner.getOrgCode())){
                result.put("ret", "1");
                result.put("errcode", '1');
                result.put("errmsg", "你无权限操作该合伙人!");
                return result;
            }
        }else{
            result.put("ret", "1");
            result.put("errcode", '1');
            result.put("errmsg", "该合伙人在展业库中不存在");
            return result;
        }

        result = qywxService.addSign( userId, tagId);
        if("0".equals(result.getString("ret"))){
            TagDto tagDto=qywxService.getTagDtoByTagId(tagId);
            if("1".equals(operateFlag)){
                dataMonitorService.save("wechat_member_mark","addSign",partner.getId().toString(),request.getHeader("staffNO"),
                        "给合伙人:"+userId+"打"+tagDto.getTagName()+"标签");
            }else{
                dataMonitorService.save("tag_manage","addSign",partner.getId().toString(),request.getHeader("staffNO"),
                        "给合伙人:"+userId+"打"+tagDto.getTagName()+"标签");
            }
        }

        log.info("增加成员标签：QywxController--addSign结束 result={}",result);
        return result;
    }

    @RequestMapping(value = "/delSign", method = RequestMethod.GET, name = "删除成员标签")
    public JSONObject delSign(String userId,long tagId,String operateFlag,HttpServletRequest request) throws Exception
    {
        log.info("删除成员标签：QywxController--delSign开始");
        JSONObject result = new JSONObject();
        String orgCode = request.getHeader("areaCode");
        Partner partner=partnerService.getPartnerByMblNbr(userId);
        if(partner!=null){
            if(!"root".equals(orgCode) && StringUtils.isNotEmpty(orgCode) && !orgCode.equals(partner.getOrgCode())){
                result.put("ret", "1");
                result.put("errcode", '1');
                result.put("errmsg", "你无权限操作该合伙人!");
                return result;
            }
        }else{
            result.put("ret", "1");
            result.put("errcode", '1');
            result.put("errmsg", "该合伙人在展业库中不存在");
            return result;
        }
        result = qywxService.delSign( userId, tagId);
        if("0".equals(result.getString("ret"))){
            TagDto tagDto=qywxService.getTagDtoByTagId(tagId);
            if("1".equals(operateFlag)){
                dataMonitorService.save("wechat_member_mark","delSign",partner.getId().toString(),request.getHeader("staffNO"),
                        "给合伙人:"+userId+"移除"+tagDto.getTagName()+"标签");
            }else{
                dataMonitorService.save("tag_manage","delSign",partner.getId().toString(),request.getHeader("staffNO"),
                        "给合伙人:"+userId+"移除"+tagDto.getTagName()+"标签");
            }
        }
        log.info("删除成员标签：QywxController--delSign结束 result={}",result);
        return result;

    }
}
