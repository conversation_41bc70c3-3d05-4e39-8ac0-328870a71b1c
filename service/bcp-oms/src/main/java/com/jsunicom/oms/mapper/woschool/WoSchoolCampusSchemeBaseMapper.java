package com.jsunicom.oms.mapper.woschool;


import com.jsunicom.oms.dto.scheme.MerchantVo;
import com.jsunicom.oms.dto.scheme.SchoolCampusSchemeVo;
import com.jsunicom.oms.po.WoSchoolCampusSchemeBase;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WoSchoolCampusSchemeBaseMapper {
    int deleteByPrimaryKey(Long schemeId);

    Long insert(WoSchoolCampusSchemeBase record);

    Long insertSelective(WoSchoolCampusSchemeBase record);

    WoSchoolCampusSchemeBase selectByPrimaryKey(Long schemeId);

    int updateByPrimaryKeySelective(WoSchoolCampusSchemeBase record);

    int updateByPrimaryKey(WoSchoolCampusSchemeBase record);

    List<SchoolCampusSchemeVo> selectSchemeList(@Param(value = "campusId") String campusId, @Param(value = "orgCode") String orgCode);

    List<MerchantVo> selectUnbindTeam(@Param(value = "campusId") String campusId, @Param(value = "schemeYear") String schemeYear, @Param(value = "marketingType") String marketingType);

    List selectTeamGroup(@Param(value = "list")List<String> merchantIds);
}