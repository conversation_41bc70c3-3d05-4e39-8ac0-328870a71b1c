package com.jsunicom.oms.service.order;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.jsunicom.common.core.entity.po.TdMSysDict;
import com.jsunicom.common.core.entity.user.UserInfo;
import com.jsunicom.common.core.util.Result;
import com.jsunicom.oms.po.order.dto.OrderDTO;
import com.jsunicom.oms.po.order.req.CustPhotoReq;
import com.jsunicom.oms.po.order.req.SchoolPayQueryReq;
import com.jsunicom.oms.po.order.rsp.CustPhotoRsp;
import com.jsunicom.oms.po.order.rsp.SchoolPayQueryRsp;
import com.jsunicom.oms.po.order.vo.OrderVo;

import java.text.ParseException;

public interface OrderQueryService {

    PageInfo<OrderVo> qryOrderInfo(OrderDTO orderDTO, UserInfo userInfo) throws ParseException, Exception;

    Result qryOrderDetails(OrderDTO orderDTO) throws Exception;

    Result qryOrderCheckInfo(JSONObject param) throws Exception;

    Result qryStaffList(JSONObject jsonObject);

    Result qryAreaList(JSONObject param);

    Result qryDeveloperInfo(String orderId);

    SchoolPayQueryRsp schoolPayQuery(SchoolPayQueryReq reqInfo);

    CustPhotoRsp getOrderCustPhoto(CustPhotoReq reqInfo);

    TdMSysDict getSysDictByTypeAndKey(String paramType, String paramKey);

    Result qryOrderCheckInfoH5(JSONObject param) throws Exception;

    PageInfo<OrderVo> qryOrderInfoH5(OrderDTO orderDTO, UserInfo userInfo)throws Exception;
}
