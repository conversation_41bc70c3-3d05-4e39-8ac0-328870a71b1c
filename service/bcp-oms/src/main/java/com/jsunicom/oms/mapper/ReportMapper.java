package com.jsunicom.oms.mapper;

import com.jsunicom.oms.dto.report.CampusChannelReportDto;
import com.jsunicom.oms.dto.report.ExtendReportDto;
import com.jsunicom.oms.dto.report.ReportDto;
import com.jsunicom.oms.model.woshcool.WoSchoolParamDto;
import com.jsunicom.oms.po.WoScYiMeeting;
import com.jsunicom.oms.po.WoScYiMeetingExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ReportMapper {
    List<Map<String,Object>> queryMeettingAndActivity(WoSchoolParamDto paramDto);

    List<Map<String,Object>> queryTaskCompletionRate(WoSchoolParamDto paramDto);

    List<ReportDto> queryCompletionRateByEarpchy(Map<String,Object> map);

    List<ExtendReportDto> queryExtendReport(Map<String,Object> map);

    /**
     * 查询校园渠道发展量报表
     * @param paramMap 查询参数
     * @return 校园渠道发展量列表
     */
    List<CampusChannelReportDto> queryCampusChannelReport(Map<String,Object> paramMap);
}
