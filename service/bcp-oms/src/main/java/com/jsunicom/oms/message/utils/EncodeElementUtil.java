package com.jsunicom.oms.message.utils;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

public class EncodeElementUtil {

    public static String EncodeUtf8 (String message_template, String key){
        JSONObject jsonobject = JSON.parseObject(message_template);
        if  (jsonobject.containsKey("refer_words") && jsonobject.getJSONObject("refer_words").containsKey(key)){
            try {
                System.out.println("translate utf8.......");
                jsonobject.getJSONObject("refer_words").put(key, URLEncoder.encode(jsonobject.getJSONObject("refer_words").getString(key),"UTF-8"));
            } catch (UnsupportedEncodingException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        // System.out.println("jsonobject.toString()");
        return jsonobject.toString();
    }

}

