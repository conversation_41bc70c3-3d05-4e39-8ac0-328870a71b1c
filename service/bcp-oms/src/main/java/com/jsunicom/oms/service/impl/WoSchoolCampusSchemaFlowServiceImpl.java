package com.jsunicom.oms.service.impl;


import com.jsunicom.oms.common.enums.FlowStatusEnum;
import com.jsunicom.oms.common.enums.IsLatestFlowEnum;
import com.jsunicom.oms.mapper.woschool.WoSchoolCampusSchemaFlowMapper;
import com.jsunicom.oms.po.UserRoleInfo;
import com.jsunicom.oms.po.WoSchoolCampusSchemaFlow;
import com.jsunicom.oms.service.WoSchoolCampusSchemaFlowService;
import com.jsunicom.oms.utils.UserContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
* <AUTHOR>
* @description 针对表【wo_school_campus_schema_flow(一校一策流程审批状态表)】的数据库操作Service实现
* @createDate 2025-02-26 14:47:22
*/
@Service
public class WoSchoolCampusSchemaFlowServiceImpl implements WoSchoolCampusSchemaFlowService {

    @Resource
    private WoSchoolCampusSchemaFlowMapper woSchoolCampusSchemaFlowMapper;

    @Override
    public int insertselectRowsByChemaFlow(WoSchoolCampusSchemaFlow woSchoolCampusSchemaFlow) {

        //校验该数据是否已经提交过申请
        int existCount = woSchoolCampusSchemaFlowMapper.selectRowsByCchemaId(woSchoolCampusSchemaFlow.getSchemaId());

        if (existCount > 0) {
            //提交过就是否最新状态修改为否
            woSchoolCampusSchemaFlowMapper.updateWoSchoolCampusSchemaIsLatestFlow(woSchoolCampusSchemaFlow.getSchemaId());
        }
        UserRoleInfo user = UserContext.getUser();

        //本地存储一校一策审批状态
        woSchoolCampusSchemaFlow.setFlowStatus(FlowStatusEnum.APPROVAL_IS_PENDING.getStatus());
        woSchoolCampusSchemaFlow.setIsLatestFlow(IsLatestFlowEnum.IS_LATEST_FLOW.getStatus());
        woSchoolCampusSchemaFlow.setCreateBy(user.getStaffName());
        int insertRows = woSchoolCampusSchemaFlowMapper.insertWoSchoolCampusSchemaFlowRow(woSchoolCampusSchemaFlow);

        return insertRows;
    }
}




