package com.jsunicom.oms.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.common.result.ResultEnum;
import com.jsunicom.oms.common.result.ResultUtil;
import com.jsunicom.oms.controller.base.AbstractSimpleController;
import com.jsunicom.oms.po.WoSchoolCampusBusinessOpportunity;
import com.jsunicom.oms.po.WoSchoolCampusExtend;
import com.jsunicom.oms.po.WoSchoolCampusNetworkInfo;
import com.jsunicom.oms.po.WoSchoolCampusProjectInfo;
import com.jsunicom.oms.service.CampusExtendService;
import com.jsunicom.oms.service.CampusNetworkInfoService;
import com.jsunicom.oms.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.Year;
import java.util.Date;
import java.util.List;

/**
 * Project:CampusMarketingInfoController
 * Author:lilj
 * Date:2024/11/21
 * Description:
 */
@Slf4j
@RestController
@RequestMapping(value = "/campusExtend")
public class CampusExtendController extends AbstractSimpleController  {

    @Resource
    private CampusExtendService campusService;

    @RequestMapping(value = "/getExtendList", method = RequestMethod.POST, name = "查询校园项目信息")
    public CustomResult getExtendList(@RequestBody JSONObject params){

        log.info("进入getExtendList方法："+ JSONObject.toJSONString(params));
        if(!params.containsKey("campusId")){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
        }
        if(!params.containsKey("projectDate")){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
        }
        int pageNum = params.getInteger("pageNo");
        int pageSize = params.getInteger("pageSize");
        String campusId =  params.getString ("campusId");
        String projectDate =  params.getString ("projectDate");

        WoSchoolCampusExtend record = new WoSchoolCampusExtend();
        record.setCampusId(Long.valueOf(campusId));
        List<WoSchoolCampusExtend> recordList = campusService.getCollegeList(record);

        if(recordList.isEmpty()){
            return ResultUtil.error(ResultEnum.DATA_IS_NULL.getrespCode(),"未查询到协议信息！");
        }

        JSONObject recordProject = new JSONObject();
        recordProject.put("campusId",campusId);
        recordProject.put("projectDate",projectDate);
        PageHelper.startPage(pageNum, pageSize);

        List<WoSchoolCampusProjectInfo> recordProjectList = campusService.getProjectInfoList(recordProject);

/*        if(recordProjectList.isEmpty()){
            JSONObject object = new JSONObject();
            object.put("projectNum",0);
            object.put("projectAmout",0);
            return ResultUtil.errorByJSONObject(object);
        }*/

        JSONObject object = new JSONObject();
        JSONArray objectArray = new JSONArray();
        object.put("isSignCooperationAgreement", recordList.get(0).getIsSignCooperationAgreement()!= null?recordList.get(0).getIsSignCooperationAgreement():"");
        object.put("agreementStartDate", DateUtils.getDayByTime(recordList.get(0).getAgreementStartDate()));
        object.put("agreementEndDate", DateUtils.getDayByTime(recordList.get(0).getAgreementEndDate1()));
        int PROJECT_AMOUNT = 0;
        for (WoSchoolCampusProjectInfo woSchoolCampusProjectInfo : recordProjectList){
                JSONObject projectInfo  = new JSONObject();

                PROJECT_AMOUNT += woSchoolCampusProjectInfo.getProjectAmount()==null?0:Integer.parseInt(woSchoolCampusProjectInfo.getProjectAmount());
                projectInfo.put("projectName", woSchoolCampusProjectInfo.getProjectName()!= null?woSchoolCampusProjectInfo.getProjectName():"");
                projectInfo.put("projectAmount", woSchoolCampusProjectInfo.getProjectAmount()!= null?woSchoolCampusProjectInfo.getProjectAmount():"");
                projectInfo.put("projectType", woSchoolCampusProjectInfo.getBusinessType()!= null?woSchoolCampusProjectInfo.getBusinessType():"");
                projectInfo.put("projectDate", DateUtils.getDayByMonth(woSchoolCampusProjectInfo.getStartDate())+"-"+DateUtils.getDayByMonth(woSchoolCampusProjectInfo.getEndDate()));
                objectArray.add(projectInfo);
        }
        object.put("projectNum", objectArray.size());
        PageInfo<WoSchoolCampusProjectInfo> pageInfo = new PageInfo<>(recordProjectList);
        object.put("total", pageInfo.getTotal());
        object.put("projectAmout", PROJECT_AMOUNT);
        object.put("projectInfo", objectArray);
        return ResultUtil.successByJSONObject(object);
    }

    @RequestMapping(value = "/getOpportunityList", method = RequestMethod.POST, name = "查询校园商机信息")
    public CustomResult getOpportunityList(@RequestBody JSONObject params){

        log.info("进入getOpportunityList方法："+ JSONObject.toJSONString(params));
        if(!params.containsKey("campusId")){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
        }
        if(!params.containsKey("opportunityDate")){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
        }
        int pageNum = params.getInteger("pageNo");
        int pageSize = params.getInteger("pageSize");

        String campusId =  params.getString ("campusId");
        String opportunityDate =  params.getString ("opportunityDate");

        WoSchoolCampusBusinessOpportunity recordProject = new WoSchoolCampusBusinessOpportunity();
        recordProject.setCampusId(Long.valueOf(campusId));
        recordProject.setEnterDate(opportunityDate);
        PageHelper.startPage(pageNum, pageSize);

        List<WoSchoolCampusBusinessOpportunity> businessOpportunityList = campusService.getBusinessOpportunityList(recordProject);

        if(businessOpportunityList.isEmpty()){
            JSONObject object = new JSONObject();
            object.put("projectNum",0);
            object.put("projectAmout",0);
            return ResultUtil.errorByJSONObject(object);        }

        JSONObject object = new JSONObject();
        JSONArray objectArray = new JSONArray();
        object.put("projectNum", businessOpportunityList.size());
        int PROJECT_AMOUNT = 0;
        for (WoSchoolCampusBusinessOpportunity woSchoolCampusBusinessOpportunity : businessOpportunityList){
            JSONObject projectInfo  = new JSONObject();

            PROJECT_AMOUNT += woSchoolCampusBusinessOpportunity.getProjectAmount()==null?0:Integer.parseInt(woSchoolCampusBusinessOpportunity.getProjectAmount());
            projectInfo.put("projectName", woSchoolCampusBusinessOpportunity.getProjectName()!= null?woSchoolCampusBusinessOpportunity.getProjectName():"");
            projectInfo.put("projectAmount", woSchoolCampusBusinessOpportunity.getProjectAmount()!= null?woSchoolCampusBusinessOpportunity.getProjectAmount():"");
            projectInfo.put("projectType", woSchoolCampusBusinessOpportunity.getBusinessType()!= null?woSchoolCampusBusinessOpportunity.getBusinessType():"");
            projectInfo.put("projectDate", woSchoolCampusBusinessOpportunity.getEnterDate()!= null?woSchoolCampusBusinessOpportunity.getEnterDate():"");
            objectArray.add(projectInfo);

        }
        PageInfo<WoSchoolCampusBusinessOpportunity> pageInfo = new PageInfo<>(businessOpportunityList);
        object.put("total", pageInfo.getTotal());
        object.put("projectAmout", PROJECT_AMOUNT);
        object.put("businessInfo", objectArray);

        return ResultUtil.successByJSONObject(object);
    }

    @RequestMapping(value = "/saveExtendInfo", method = RequestMethod.POST, name = "校园项目信息-保存战略信息")
    public CustomResult saveExtendInfo(@RequestBody JSONObject params){

        log.info("saveExtendInfo："+JSONObject.toJSONString(params));

        try {
            WoSchoolCampusExtend record = JSONObject.parseObject(JSONObject.toJSONString(params), WoSchoolCampusExtend.class);
            List<WoSchoolCampusExtend> recruitInfoList = campusService.getCollegeList(record);

            if(recruitInfoList.isEmpty()){

                WoSchoolCampusExtend recordTemp = JSONObject.parseObject(JSONObject.toJSONString(params), WoSchoolCampusExtend.class);
                recordTemp.setCreateTime(new Date());
                campusService.saveCollegeExtendInfo(recordTemp);

                return ResultUtil.success(ResultEnum.SUCCESS.getrespCode(), "新增数据成功");

            }else{
                WoSchoolCampusExtend recordTemp = JSONObject.parseObject(JSONObject.toJSONString(params), WoSchoolCampusExtend.class);
                recordTemp.setUpdateTime(new Date());
                campusService.updateCollegeInfo(recordTemp);

                return ResultUtil.success(ResultEnum.SUCCESS.getrespCode(),"数据更新成功！");
            }

        }catch (Exception e){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"保存失败！");
        }

    }

    @RequestMapping(value = "/updateExtendInfo", method = RequestMethod.POST, name = "校园项目信息-更新信息")
    public CustomResult updateExtendInfo(@RequestBody JSONObject params){

        log.info("updateExtendInfo："+JSONObject.toJSONString(params));

        try {
            if(!params.containsKey("campusId")){
                return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
            }

            WoSchoolCampusExtend record = JSONObject.parseObject(JSONObject.toJSONString(params), WoSchoolCampusExtend.class);
            campusService.updateCollegeInfo(record);

            return ResultUtil.success(ResultEnum.SUCCESS.getrespCode(), "更新数据成功");

        }catch (Exception e){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"更新失败！");
        }
    }

    @RequestMapping(value = "/deleteMarketingInfo", method = RequestMethod.POST, name = "营销信息-删除信息")
    public CustomResult deleteMarketingInfo(@RequestBody JSONObject params){

        log.info("进入deleteMarketingInfo方法："+JSONObject.toJSONString(params));

        try {

            if(!params.containsKey("campusId")){
                return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
            }

            String campusId =  params.getString("campusId");
            // 删除院系信息
            campusService.deleteCollegeInfo(Long.valueOf(campusId));


            return ResultUtil.success(ResultEnum.SUCCESS.getrespCode(), "删除数据成功");

        }catch (Exception e){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"删除失败！");
        }

    }

    @RequestMapping(value = "/getExtendAgreement", method = RequestMethod.POST, name = "查询校园战略信息")
    public CustomResult getExtendAgreement(@RequestBody JSONObject params){

        log.info("进入getExtendList方法："+ JSONObject.toJSONString(params));
        if(!params.containsKey("campusId")){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),"参数错误！");
        }

        String campusId =  params.getString ("campusId");

        WoSchoolCampusExtend record = new WoSchoolCampusExtend();
        record.setCampusId(Long.valueOf(campusId));
        List<WoSchoolCampusExtend> recordList = campusService.getCollegeList(record);

        if(recordList.isEmpty()){
            return ResultUtil.error(ResultEnum.DATA_IS_NULL.getrespCode(),"未查询到校区信息！");
        }

        JSONObject object = new JSONObject();
        object.put("isSignCooperationAgreement", recordList.get(0).getIsSignCooperationAgreement()!= null?recordList.get(0).getIsSignCooperationAgreement():"");
        object.put("agreementStartDate", DateUtils.getDayByTime(recordList.get(0).getAgreementStartDate()));
        object.put("agreementEndDate", DateUtils.getDayByTime(recordList.get(0).getAgreementEndDate1()));

        return ResultUtil.successByJSONObject(object);
    }

}
