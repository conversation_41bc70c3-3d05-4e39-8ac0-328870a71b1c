package com.jsunicom.oms.utils;

import org.apache.commons.lang3.StringUtils;

public class DesensitizeUtil {

    /**
     * [中文姓名] 只显示第一个汉字，其他隐藏为2个星号<例子：李**>
     *
     * @param fullName
     * @return
     */
    public static String chineseName(String fullName) {
        if (StringUtils.isBlank(fullName)) {
            return "";
        }
        String name = StringUtils.left(fullName, 1);
        return StringUtils.rightPad(name, StringUtils.length(fullName), "*");
    }

    /**
     * [中文姓名] 只显示第一个汉字，其他隐藏为2个星号<例子：李**>
     *
     * @param familyName
     * @param givenName
     * @return
     */
    public static String chineseName(String familyName, String givenName) {
        if (StringUtils.isBlank(familyName) || StringUtils.isBlank(givenName)) {
            return "";
        }
        return chineseName(familyName + givenName);
    }

    /**
     * [身份证号] 110****58，前面保留3位明文，后面保留2位明文
     *
     * @param id
     * @return
     */
    public static String identificationNum(String id) {
        if (StringUtils.isBlank(id)) {
            return "";
        }
        return StringUtils.left(id, 6).concat(StringUtils.removeStart(StringUtils.leftPad(StringUtils.right(id, 4), StringUtils.length(id), "*"), "******"));
    }

    /**
     * [固定电话] 后四位，其他隐藏<例子：****1234>
     *
     * @param num
     * @return
     */
    public static String fixedPhone(String num) {
        if (StringUtils.isBlank(num)) {
            return "";
        }
        return StringUtils.leftPad(StringUtils.right(num, 4), StringUtils.length(num), "*");
    }

    /**
     * [手机号码] 前四位，后四位，其他隐藏<例子:1381****1234>
     *
     * @param num
     * @return
     */
    public static String mobilePhone(String num) {
        if (StringUtils.isBlank(num)) {
            return "";
        }
        return StringUtils.left(num, 4).concat(StringUtils.removeStart(StringUtils.leftPad(StringUtils.right(num, 4), StringUtils.length(num), "*"), "***"));
    }

    /**
     * [地址] 只显示到地区，不显示详细地址；我们要对个人信息增强保护<例子：北京市海淀区****>
     *
     * @param address
     * @param sensitiveSize
     *            敏感信息长度
     * @return
     */
    public static String address(String address, int sensitiveSize) {
        if (StringUtils.isBlank(address)) {
            return "";
        }
        int length = StringUtils.length(address);
        //return StringUtils.rightPad(StringUtils.left(address, length - sensitiveSize), length, "*");
        //return StringUtils.left(address, sensitiveSize)+"******"+StringUtils.right(address, 12);
        return StringUtils.rightPad(StringUtils.left(address, sensitiveSize), length, "*");
    }

    /**
     * [电子邮箱] 邮箱前缀仅显示第一个字母，前缀其他隐藏，用星号代替，@及后面的地址显示<例子:g**@163.com>
     *
     * @param email
     * @return
     */
    public static String email(String email) {
        if (StringUtils.isBlank(email)) {
            return "";
        }
        int index = StringUtils.indexOf(email, "@");
        if (index <= 1)
            return email;
        else
            return StringUtils.rightPad(StringUtils.left(email, 1), index, "*").concat(StringUtils.mid(email, index, StringUtils.length(email)));
    }

    /**
     * [银行卡号] 前四位，后四位，其他用星号隐藏每位1个星号<例子:6222600**********1234>
     *
     * @param cardNum
     * @return
     */
    public static String bankCard(String cardNum) {
        if (StringUtils.isBlank(cardNum)) {
            return "";
        }
        return StringUtils.left(cardNum, 4).concat(StringUtils.removeStart(StringUtils.leftPad(StringUtils.right(cardNum, 4), StringUtils.length(cardNum), "*"), "******"));
    }

    /**
     * [公司开户银行联号] 公司开户银行联行号,显示前两位，其他用星号隐藏，每位1个星号<例子:12********>
     *
     * @param code
     * @return
     */
    public static String cnapsCode(String code) {
        if (StringUtils.isBlank(code)) {
            return "";
        }
        return StringUtils.rightPad(StringUtils.left(code, 2), StringUtils.length(code), "*");
    }


    public static void main(String[] args){
        System.out.println("QQ:*********,脱敏后:"+DesensitizeUtil.fixedPhone("*********"));
        System.out.println("学生姓名:张小三,脱敏后:"+DesensitizeUtil.chineseName("张小三"));
        System.out.println("联系电话:15651601008,脱敏后:"+DesensitizeUtil.mobilePhone("15651601008"));
        System.out.println("身份证号码:320981196508091453,脱敏后:"+DesensitizeUtil.identificationNum("320981196508091453"));
        System.out.println("第二联系方式:15651601156,脱敏后:"+DesensitizeUtil.mobilePhone("15651601156"));
        System.out.println("联通号码:15651602356,脱敏后:"+DesensitizeUtil.mobilePhone("15651602356"));
        System.out.println("配送地址:江苏省南京市建邺区庐山路230号联通大厦,脱敏后:"+DesensitizeUtil.desensitizedAddress("江苏省南京市建邺区庐山路230号联通大厦"));
    }

    public static String desensitizedAddress(String address) {
        if (StringUtils.isNotBlank(address)) {
            return StringUtils.left(address, 6).concat(
                    StringUtils.removeStart(
                            StringUtils.leftPad(
                                   StringUtils.right(address, address.length() - 11),
                                   StringUtils.length(address),
                             "*"),
                    "***")
                    );
        }
        return address;
    }
}
