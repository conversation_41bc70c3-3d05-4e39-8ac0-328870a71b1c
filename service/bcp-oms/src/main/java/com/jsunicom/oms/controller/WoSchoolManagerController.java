package com.jsunicom.oms.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.common.exception.BusinessException;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.common.result.ResultEnum;
import com.jsunicom.oms.common.result.ResultUtil;
import com.jsunicom.oms.controller.base.AbstractSimpleController;
import com.jsunicom.oms.dto.user.UserInfoDto;
import com.jsunicom.oms.entity.ImportInfo;
import com.jsunicom.oms.entity.SchoolListInfoDto;
import com.jsunicom.oms.entity.WoSchoolMsgconfExt;
import com.jsunicom.oms.mapper.UserRoleInfoMapper;
import com.jsunicom.oms.model.partner.Partner;
import com.jsunicom.oms.model.woshcool.SchoolInfo;
import com.jsunicom.oms.model.woshcool.WoSchoolMsgNumConfDto;
import com.jsunicom.oms.po.UserRoleInfo;
import com.jsunicom.oms.po.UserRoleInfoExample;
import com.jsunicom.oms.po.WoSchoolMsgconf;
import com.jsunicom.oms.po.WoSchoolMsgsendnumConf;
import com.jsunicom.oms.service.CampusBindFacade;
import com.jsunicom.oms.service.DataMonitorService;
import com.jsunicom.oms.service.PartnerFacade;
import com.jsunicom.oms.service.WoSchoolService;
import com.jsunicom.oms.utils.ExcelException;
import com.jsunicom.oms.utils.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-03-22-17:34
 */
@RestController
@RequestMapping(value = "/woschool/manager")
@Slf4j
public class WoSchoolManagerController extends AbstractSimpleController {
    @Autowired
    private WoSchoolService woSchoolService;
    @Autowired
    private DataMonitorService dataMonitorService;
    @Autowired
    private PartnerFacade partnerService;
    @Autowired
    private CampusBindFacade campusBindService;
    @Autowired
    private UserRoleInfoMapper userRoleInfoMapper;
    @RequestMapping(value = "/queryWoSchoolMsgInfo.json")
    public CustomResult queryWoSchoolMsgInfo(WoSchoolMsgconfExt woSchoolMsgConf, Integer pageNo, Integer pageSize, HttpServletRequest request) {
        PageInfo<WoSchoolMsgconfExt> pageList;
        log.info("进入 审核短信模板 WoSchoolManagerController.queryWoSchoolMsgInfo.json");
            if (!StringUtils.equals("root", request.getHeader("areaCode")) && !StringUtils.equals("", request.getHeader("areaCode"))) {
                woSchoolMsgConf.setOrgCode(request.getHeader("areaCode"));
            }
            // 调用接口查询
            pageList = woSchoolService.findSchoolMsgInfoByPage(woSchoolMsgConf, pageNo, pageSize);
            log.info("查询学校信息成功,total:{}", pageList.getTotal());
            HashMap map=new HashMap();
            map.put("rsList",pageList.getList());
            map.put("totalCount",pageList.getTotal());
            return ResultUtil.success(map);
    }
    @RequestMapping(value = "updateWoSchoolMsgInfo.json")
    public Object updateWoSchoolMsgInfo(WoSchoolMsgconfExt woSchoolMsgConf,HttpServletRequest request) {
            log.info("进入 审核短信模板 WoSchoolManagerController.updateWoSchoolMsgInfo");
            //更新原表数据
            WoSchoolMsgconf msgConf = new WoSchoolMsgconf();
            msgConf.setId(woSchoolMsgConf.getId());
            WoSchoolMsgconf woSchoolMsgConfOld = woSchoolService.querySchoolMsgInfo(msgConf);
            woSchoolMsgConf.setUpdateBy(request.getHeader("serialNumber"));
            woSchoolService.updateWoSchoolMsgConf(woSchoolMsgConf);
            dataMonitorService.save("woschoolMsfConf", "auditMsgConf", Long.toString(woSchoolMsgConf.getId()), request.getHeader("staffNo"), woSchoolMsgConf, woSchoolMsgConfOld);
            log.info("调用审核短信模板 WoSchoolManagerController.updateWoSchoolMsgInfo 成功");
            return "success";
    }
    @RequestMapping(value = "queryWoSchoolMsgSendNum.json")
    public CustomResult queryWoSchoolMsgSendNum(WoSchoolMsgsendnumConf woSchoolMsgNumConf, Integer pageNo, Integer pageSize) {
        PageInfo<WoSchoolMsgsendnumConf> pageList;
            // 调用接口查询
            pageList = woSchoolService.findWoSchoolMsgSendNumByPage(woSchoolMsgNumConf, pageNo, pageSize);
            log.info("查询模板对应号码成功,total:{}", pageList.getTotal());
            HashMap res=new HashMap();
            res.put("rsList",pageList.getList());
            res.put("totalCount",pageList.getTotal());
            return ResultUtil.success(res);
    }
    /**
     * 查询学校商户列表
     *
     * @param schoolName
     * @param schoolManagerMbl
     * @param orgCode
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "schoolList.json")
    public CustomResult listMerchant(String schoolName, String schoolManagerMbl, String orgCode, Integer pageNumber, Integer pageSize, Long schoolId,HttpServletRequest request) {
           String serialNumber = request.getHeader("serialNumber");
        UserRoleInfoExample example=new UserRoleInfoExample();
        example.createCriteria().andSerialNumberEqualTo(serialNumber);
        List<UserRoleInfo> userRoleInfos = userRoleInfoMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(userRoleInfos)){
            UserRoleInfo userRoleInfo = userRoleInfos.get(0);
            String roleCode = userRoleInfo.getRoleCode();
            if ("3".equals(roleCode)){
                schoolManagerMbl=userRoleInfo.getSerialNumber();
            }
        }
        // 判断分页参数是否正确
            if (pageNumber == null || pageNumber < 1) {
                return ResultUtil.error("9999","分页参数错误");
            }
            if (pageSize == null || pageSize < 1) {
                return ResultUtil.error("9999","分页参数错误");
            }
        /*if (StringUtils.isNotBlank(user.getBusLine())) {//所属条线不是全部条线
            String[] values = user.getBusLine().split(",");
            List<String> list = Arrays.asList(values);
            if (!list.contains("20")) {//所属条线不包含校园条线
                throw new BusinessException("", "您当前非校园条线无法查询沃校园信息！");
            }
        }*/

            //PagedResult<SchoolListInfoDto> pageList = woSchoolService.findByPageWithManager(schoolName, schoolManagerMbl, pageNumber, page.getPageSize(), user.getOrgCode(),orgCode,schoolId);
            PageInfo<SchoolListInfoDto> pageList = woSchoolService.findByPageWithManagerNew(schoolName, schoolManagerMbl, pageNumber, pageSize, request.getHeader("areaCode"), orgCode, schoolId);
            log.info("查询学校信息成功,total:{}", pageList.getTotal());

            // 封装返回数据 返回结果
            HashMap result = new HashMap();
            JSONObject data = new JSONObject();
            data.put("list", pageList.getList());
            data.put("totalNumber", pageList.getTotal());
            result.put("data", data);
            log.info("查询校园团队列表成功");
            return ResultUtil.success(result);
    }
    @RequestMapping(value = "queryMerchantMember.json", method = RequestMethod.POST)
    public JSONObject querySchoolMember(@RequestBody Partner partner) {
        log.info("进入查询团队长和团队成员信息方法  参数：" + partner);
        JSONObject result =null;
            if (StringUtils.isNotEmpty(partner.getMblNbr())) {
                result = partnerService.queryMerchantMember(partner.getMblNbr(), partner.getMerchantId());
                log.info("查询团队长和团队成员信息结果:" + result);
            } else {
                result.put("ret", "1");
                result.put("errcode", "1");
                result.put("errmsg", "手机号码为空，请输入");
                result.put("id", "");
                result.put("name", "");
                result.put("phone", "");
            }
        log.info("查询团队长和团队成员信息结果:" + result);
        return result;
    }
    @RequestMapping(value = "updateMerchantCaptain.json", method = RequestMethod.POST)
    public CustomResult updateMerchantCaptain(@RequestBody Partner partner,HttpServletRequest request) {
        log.info("进入更换团队长方法 参数：" + partner);
        CustomResult result = new CustomResult();

        if (null != partner.getId() && null != partner.getMerchantId()) {
            //查询原团队团队长信息
            Partner merchantManager = partnerService.findMerchantManager(partner.getMerchantId());
            if (merchantManager == null) {//没有团队长 找到目标团队成员  修改is_mer_admin=1 更新merchant的负责人信息
                result = partnerService.updateMerchantCaptain(partner, request.getHeader("staffName"),request.getHeader("staffNo"));
            } else if (merchantManager.getId() == partner.getId()) {//团队长没有改变
                return ResultUtil.success();
            } else {//更换团队长
                result = partnerService.updateTwoMerchantCaptain(merchantManager, partner, request.getHeader("staffName"),request.getHeader("staffNo"));
            }
            //result = woSchoolService.updateSchoolManager(schoolManagerId,schoolId,userInfo.getAcctNo());
            //dataMonitorFacade.save("woschool","updateWoSchoolInfo",Long.toString(schoolId),userInfo.getAcctNo(),"校区经理由"+oldSchoolManager+"改为"+newSchoolManager);
        } else {
            log.info("进入更换团队长方法 失败:成员id或团队id不能为空");
            return ResultUtil.error("9999","成员id或团队id不能为空");
        }
        log.info("更换团队长方法执行结果：" + result);
        return result;
    }
    /**
     * 根据学校ID查询学校登录方式
     *
     * @param schoolId
     * @return
     */
    @RequestMapping(value = "querySchoolInfo.json")
    public CustomResult querySchoolInfo(Long schoolId) {
        SchoolInfo schoolInfo;
            schoolInfo = woSchoolService.getSchoolById(schoolId);
        return ResultUtil.success(schoolInfo);
    }
    /**
     * 配置学校登录方式
     *
     * @param schoolInfo
     * @return
     */
    @RequestMapping(value = "updateSchoolLoginType.json")
    public Object updateSchoolLoginType(SchoolInfo schoolInfo,HttpServletRequest request) {
            updateSchoolLoginType(schoolInfo, request.getHeader("staffNo"),Long.parseLong(request.getHeader("userPid")));
        return "success";
    }
    private void updateSchoolLoginType(SchoolInfo schoolInfo, String staffNo,Long userPid) {
        SchoolInfo oldWoSchoolInfo = woSchoolService.getSchoolById(schoolInfo.getId());

        //登录方式有修改则更新并记录日志
        if (null != oldWoSchoolInfo) {
            if (null != oldWoSchoolInfo.getLoginType()) {
                dataMonitorService.save("woschool", "updateWoSchoolInfo", Long.toString(schoolInfo.getId()), staffNo, "学校登录方式由" + oldWoSchoolInfo.getLoginType() + "改为" + schoolInfo.getLoginType());
                woSchoolService.updateSchoolInfo(schoolInfo, userPid);
            }
        }
    }
    /**
     * 根据手机号和地市代码查询销售经理
     *
     * @param managerPhone
     * @param orgCode
     * @return
     */
    @RequestMapping(value = "querySchoolMember.json")
    public JSONObject querySchoolMember(String managerPhone, String orgCode) {

        JSONObject result = null;

            if (StringUtils.isNotEmpty(managerPhone)) {
                result = woSchoolService.querySchoolMember(managerPhone, orgCode);
            } else {
                result.put("ret", "1");
                result.put("errcode", "1");
                result.put("errmsg", "手机号码为空，请输入");
                result.put("id", "");
                result.put("name", "");
                result.put("parentId", "");
            }
        return result;
    }
    /**
     * 更换校区经理
     *
     * @param schoolManagerId
     * @param schoolId
     * @return
     */
    @RequestMapping(value = "updateSchoolManager.json")
    public CustomResult updateSchoolManager(Long schoolManagerId, Long schoolId, String oldSchoolManager, String newSchoolManager,HttpServletRequest request) {
            CustomResult result = new CustomResult();

            if (null != schoolManagerId) {
                result = woSchoolService.updateSchoolManager(schoolManagerId, schoolId, request.getHeader("staffNo"));
                dataMonitorService.save("woschool", "updateWoSchoolInfo", Long.toString(schoolId), request.getHeader("staffNo"), "校区经理由" + oldSchoolManager + "改为" + newSchoolManager
                );
            } else {
                log.info("更换校区经理 fail:更换校园经理信息有误，请确认后重试！");
                return ResultUtil.error("9999","更换校园经理信息有误，请确认后重试！");
            }
            return result;
    }
    /**
     * 配置学校登录方式
     *
     * @param campusId
     * @param loginType
     * @return
     */
    @RequestMapping(value = "updateSchoolLoginTypeByCampus.json")
    public Object updateSchoolLoginTypeByCampus(Long campusId, String loginType,HttpServletRequest request) {
            List<SchoolInfo> schoolInfos = campusBindService.querySchoolByCId(campusId);
            for (SchoolInfo schoolInfo : schoolInfos) {
                schoolInfo.setLoginType(loginType);
                updateSchoolLoginType(schoolInfo, request.getHeader("staffNo"),Long.parseLong(request.getHeader("userPid")));
            }
        return "success";
    }
    /**
     * 新附近小程序查询
     *
     * @return
     */
    @RequestMapping(value = "querySchoolAppletBySchoolNew", method = RequestMethod.GET, name = "查询小程序信息（新）")
    public CustomResult querySchoolAppletBySchoolNew(HttpServletRequest request, String schoolName,
                                                                         String schoolId, String managerPhone, String orgCode,
                                                                         @RequestParam int pageNum, @RequestParam int pageSize, String appletSchoolName) {
            log.info("Entry Method: querySchoolAppletBySchoolNew() ");
            Map<String, Object> params = new HashMap<String, Object>();
            if (StringUtils.isNotEmpty(schoolName)) {
                params.put("schoolName", schoolName);
            }
            if (StringUtils.isNotEmpty(schoolId)) {
                params.put("schoolId", schoolId);
            }
            if (StringUtils.isNotEmpty(managerPhone)) {
                params.put("mblNbr", managerPhone);
            }
            if (StringUtils.isNotEmpty(orgCode)) {
                params.put("orgCode", orgCode);
            }else {
                params.put("orgCode", request.getHeader("areaCode"));
            }
            if (StringUtils.isNotEmpty(appletSchoolName)) {
                params.put("appletSchoolName", appletSchoolName);
            }

            //log.debug("params:{}", JSONObject.toJSONString(params));

            PageInfo<Map<String, Object>> pagedResult = woSchoolService.querySchoolAppletBySchoolNew(params, pageNum, pageSize);
            log.info("Exit Method: querySchoolAppletBySchoolNew() ");
            HashMap res=new HashMap();
            res.put("rsList",pagedResult.getList());
            res.put("totalCount",pagedResult.getTotal());
            return ResultUtil.success(res);
    }
    @RequestMapping(value = "/querySchoolAppletBySchoolGoodDetail", method = RequestMethod.GET, name = "查询商品及号池信息")
    public CustomResult querySchoolAppletBySchoolGoodDetail(String schoolId, String schoolCampusId) {
        log.info("Entry Method: querySchoolAppletBySchoolGoodDetail({}) ", schoolId);
        Map<String, Object> params = new HashMap<String, Object>();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(schoolId)) {
            params.put("schoolId", schoolId);
        } else {
            log.info("querySchoolAppletBySchoolGoodDetail() fail:团队id必传");
            return ResultUtil.error("9999","团队id必传");
        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(schoolCampusId)) {
            params.put("schoolCampusId", schoolCampusId);
        } else {
            log.info("querySchoolAppletBySchoolGoodDetail() fail:校区编码必传");
            return ResultUtil.error("9999","校区编码必传");

        }
        List<Map<String, Object>> goodsList = woSchoolService.querySchoolAppletBySchoolGoodDetail(params);
        if (log.isDebugEnabled()) {
            log.debug("goods list is :{}", JSONObject.toJSONString(goodsList));
        }
        log.info("Exit Method: querySchoolAppletBySchoolGoodDetail() ");
        return ResultUtil.success(goodsList);
    }
    /**
     * 更换校区经理
     *
     * @param schoolManagerId
     * @param campusId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "updateSchoolManagerByCampus.json")
    public JSONObject updateSchoolManager(Long schoolManagerId, String oldSchoolManager, String newSchoolManager, Long campusId,HttpServletRequest request) {
        JSONObject result = new JSONObject();

        if (null == campusId || 0 == campusId) {
            result.put("ret", "1");
            result.put("errcode", "1");
            result.put("errmsg", "更换校园经理信息有误，校区ID为空，请确认后重试！");
            result.put("id", "");
            result.put("name", "");
            result.put("parentId", "");
        } else if (null != schoolManagerId) {
            String userPid=request.getHeader("userPid");
            result = woSchoolService.updateSchoolManagerByCampus(schoolManagerId, Long.parseLong(userPid), campusId);
            dataMonitorService.save("woschool", "updateSchoolManagerByCampus", Long.toString(campusId), request.getHeader("staffNo"), "校区经理由" + oldSchoolManager + "改为" + newSchoolManager
            );
        } else {
            result.put("ret", "1");
            result.put("errcode", "1");
            result.put("errmsg", "更换校园经理信息有误，请确认后重试！");
            result.put("id", "");
            result.put("name", "");
            result.put("parentId", "");
        }
        return result;
    }

    /*
     * @description: 解绑校区经理
     * @author: zhaowang
     * @date: 2024/1/24 下午8:00
     * @param: [campusId, mblNbr, request]
     * @return: java.lang.Object
     **/
    @RequestMapping(value = "removeSchoolManagerByCampus.json")
    public CustomResult removeSchoolManagerByCampus(Long campusId, String mblNbr,HttpServletRequest request) {
        try {
            return campusBindService.removeSchoolManagerByCampusId(campusId,mblNbr);
        }catch (ExcelException e){
            return ResultUtil.error(ResultEnum.ERROR.getrespCode(),e.getMessage());
        }

    }

    @RequestMapping(value = "queryWoSchoolMsgSendNumDetail.json")
    public Object queryWoSchoolMsgSendNumDetail(WoSchoolMsgNumConfDto woSchoolMsgNumConfDto) {
        List<WoSchoolMsgNumConfDto> list;
        try {
            // 调用接口查询
            list = woSchoolService.findWoSchoolMsgSendNumDetail(woSchoolMsgNumConfDto);
            log.info("查询模板对应号码成功,total:{}", list.size());
        } catch (Exception e) {
            log.error("查询模板对应号码出错:{}", e);
            return ("查询模板对应号码异常，请联系管理员");
        }
        return list;
    }
    /**
     * 上传excel文件
     */
    @RequestMapping(value = "/uploadExcel.json", name = "excel上传")
    public Object uploadExcel(String schoolId, MultipartFile uploadfile, HttpServletRequest request, HttpServletResponse response) throws UnsupportedEncodingException {
        try {
            //读取excel
            Map<String, Object> dataMap = ExcelUtil.readExcel(uploadfile, new ImportInfo());
            List<String> errorList = (List<String>) dataMap.get("errorMsg");
            if (errorList.size() > 0) {
                return errorList.get(0);
            } else {
                //数据添加到数据库
                List<String> numList = (List<String>) dataMap.get("data");
                woSchoolService.saveBatchSchoolCheckNum(schoolId, numList, Long.parseLong(request.getHeader("userPid")));
            }
            return "success";
        } catch (ExcelException e) {
            log.error("导入文件格式错误", e.getMessage());
            return ("只支持.xls,.xlsx文件！");
        } catch (Exception e) {
            log.error("WoSchoolManagerController.uploadExcel error:", e.getMessage());
            return ("excel文件导入出错，请联系管理员");
        }
    }
    /**
     * 上传excel文件
     */
    @ResponseBody
    @RequestMapping(value = "/uploadExcelByCampus.json", name = "excel上传")
    public Object uploadExcel(Long campusId, MultipartFile uploadfile, HttpServletRequest request, HttpServletResponse response) throws UnsupportedEncodingException {
        try {
            //读取excel
            Map<String, Object> dataMap = ExcelUtil.readExcel(uploadfile, new ImportInfo());
            List<String> errorList = (List<String>) dataMap.get("errorMsg");
            if (errorList.size() > 0) {
                return errorList.get(0);
            } else {
                //查询
                List<SchoolInfo> schoolInfos = campusBindService.querySchoolByCId(campusId);
                List<String> numList = (List<String>) dataMap.get("data");
                for (SchoolInfo schoolInfo : schoolInfos) {
                    //数据添加到数据库
                    woSchoolService.saveBatchSchoolCheckNum(String.valueOf(schoolInfo.getId()), numList, Long.parseLong(request.getHeader("userPid")));
                }

            }
            return "success";
        } catch (ExcelException e) {
            log.error("导入文件格式错误", e.getMessage());
            return ("只支持.xls,.xlsx文件！");
        } catch (Exception e) {
            log.error("WoSchoolManagerController.uploadExcel error:", e.getMessage());
            return ("excel文件导入出错，请联系管理员");
        }
    }
}
