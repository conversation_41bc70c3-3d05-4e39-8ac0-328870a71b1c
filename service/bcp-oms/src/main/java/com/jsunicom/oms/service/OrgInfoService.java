package com.jsunicom.oms.service;

import com.jsunicom.oms.entity.CampusInfoTreeNode;
import com.jsunicom.oms.entity.OrgInfoTreeNode;
import com.jsunicom.oms.po.OrgInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-03-21-10:29
 */
public interface OrgInfoService {
    List<String> getAllTreeCode(String goodsArea);
    OrgInfoTreeNode getNodeTree(String orgCode);
    // 获取所有树
    OrgInfoTreeNode getAllOrgInfoTree();
    List<OrgInfo> findList(OrgInfo info);

    // 查询机构编码和机构名称的MAP
    Map<String, String> getCodeNameMap();
    List<com.jsunicom.oms.model.org.OrgInfo> getTheCity(String orgCode);
    List<com.jsunicom.oms.model.org.OrgInfo> findList(com.jsunicom.oms.model.org.OrgInfo info);
    // 查询所有的地市机构
    ArrayList<com.jsunicom.oms.model.org.OrgInfo> getCityDepartment();
    //查询所有机构
    ArrayList<com.jsunicom.oms.model.org.OrgInfo> getCityDepartment(String orgCode);

    OrgInfo getOrgInfo(String orgCode);

    public List<CampusInfoTreeNode> getCampusNodeTree(String orgCode);

    public List<CampusInfoTreeNode> getPartnerCampusNodeTree(String serialNumber, String orgCode);

    public Map<String, String> findAllList();
}
