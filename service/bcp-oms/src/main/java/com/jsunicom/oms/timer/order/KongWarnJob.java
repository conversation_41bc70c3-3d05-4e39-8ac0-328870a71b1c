package com.jsunicom.oms.timer.order;

import com.jsunicom.common.core.entity.po.TdMSysDict;
import com.jsunicom.oms.mapper.resource.TdMSysDictMapper;
import com.jsunicom.oms.po.order.req.SendSmsReq;
import com.jsunicom.oms.service.order.SendSmsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import com.jsunicom.common.service.KongWarnService;

import java.io.IOException;
import java.net.URI;
import java.util.*;

@Slf4j
@Configuration
@EnableScheduling
public class KongWarnJob {

    @Autowired
    private KongWarnService kongWarnService;

    @Autowired
    private TdMSysDictMapper tdMSysDictMapper;

    @Autowired
    private SendSmsService sendSmsService;

    @XxlJob("kongWarnJob")
    public ReturnT<String> kongWarnJob(String param) {
        log.info("kongWarnJob is starting work");

        TdMSysDict sysDict = new TdMSysDict();
        sysDict.setParamType("kongWarn");
        //    sysDict.setParamKey("kongWarn");
        //    TdMSysDict tdMSysDict = tdMSysDictMapper.query(sysDict);
        List<TdMSysDict> list = tdMSysDictMapper.queryAll(sysDict);
        for (int j = 0; j < list.size(); j++) {
            String sendPhone=list.get(j).getParam1();
            String url=list.get(j).getParamValue();
            String msgId=list.get(j).getParam2();
            String key=list.get(j).getParamKey();
            try{
                int code=reqGet(url);
                if(code==0){
                    log.info("kongWarnJob ====error"+"==url="+url);
                    SendSmsReq sendSmsReq = new SendSmsReq();
                    sendSmsReq.setTemplateId(msgId);
                    sendSmsReq.setSendTo(sendPhone);
                    sendSmsReq.setSendParam(key);
                    log.info("kongWarnJob ======sendSms="+sendSmsReq.toString());
                    sendSmsService.sendSms(sendSmsReq);
                };
            }catch (Exception e){
                log.info("kongWarnJob ====url="+url+"error=="+e.getMessage());
                e.printStackTrace();

            }

        }

       /*
        try {
            kongWarnService.getKongWodoService();
        }catch (Exception e){
            log.info("kongWarnJob ===getKongWodoService==error"+e.getMessage());
            SendSmsReq sendSmsReq = new SendSmsReq();
            sendSmsReq.setTemplateId(msgId);
            sendSmsReq.setSendTo(sendPhone);
            sendSmsReq.setSendParam("wodo");
            log.info("kongWarnJob ===getKongWoscService====sendSms="+sendSmsReq.toString());
            sendSmsService.sendSms(sendSmsReq);
            e.printStackTrace();
        }*/
        return ReturnT.SUCCESS;
    }

    public static int  reqGet(String url) throws Exception{
        // 创建Httpclient对象
        CloseableHttpClient httpclient = HttpClients.createDefault();
        String resultString = "";
        CloseableHttpResponse response = null;
        int code=0;
        try {
            URIBuilder builder = new URIBuilder(url);
            URI uri = builder.build();
            // 创建http GET请求
            HttpGet httpGet = new HttpGet(uri);
            // 执行请求
            response = httpclient.execute(httpGet);
            // 判断返回状态是否为200
            log.info("kongWarnJob ==reqGet===url="+url+"===Code="+response.getStatusLine().getStatusCode());
            if (response.getStatusLine().getStatusCode() == 200) {
                //    resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
                code=1;
                return code;
            }
        }catch (Exception e){
            log.info("kongWarnJob ==reqGet===url="+url+"error=="+e.getMessage());
            e.printStackTrace();
        } finally {
            httpclient.close();
        }
        return code;

    }


}
