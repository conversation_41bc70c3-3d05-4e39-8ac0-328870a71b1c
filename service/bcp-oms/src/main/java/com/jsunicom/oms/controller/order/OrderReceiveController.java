package com.jsunicom.oms.controller.order;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.jsunicom.common.core.util.Result;
import com.jsunicom.oms.po.order.req.OrderReceiveReq;
import com.jsunicom.oms.service.order.GenerateId;
import com.jsunicom.oms.service.order.OrderReceiveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.Date;
@Slf4j
@RestController
@RequestMapping("/order")
public class OrderReceiveController {

    @Autowired
    private OrderReceiveService orderReceiveService;

    @Autowired
    private GenerateId generateId;

    @PostMapping("/orderReceive")
    public Result orderReceive(@RequestBody OrderReceiveReq orderReceiveReq){
        log.info("orderReceive request is={}",orderReceiveReq.toString());
        Result respInfo = new Result();

        try {

            String orderId = generateId.getId("TF_ORD_MAIN");
            //保存订单原始数据
            orderReceiveService.saveOriginalReceiveDataToDB(orderReceiveReq, orderId);

            JSONObject data= new JSONObject();
            data=orderReceiveService.saveReceiveDataToDB(orderReceiveReq, orderId);
            respInfo.setCode(200L);
            respInfo.setMsg("下单成功");
            respInfo.setData(data);
            respInfo.setSuccess(true);


        }catch (Exception e){
            log.error("orderReceive is running erro=={}",e);
            respInfo.setCode(500L);
            respInfo.setSuccess(false);
            respInfo.setMsg("订单接收失败:"+e.getMessage());

        }
        return  respInfo;
    }
}
