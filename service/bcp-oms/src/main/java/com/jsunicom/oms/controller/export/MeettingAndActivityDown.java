package com.jsunicom.oms.controller.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class MeettingAndActivityDown extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(index = 0,value = "地市名称")
    @JSONField(name = "orgName")
    private String orgName;
    @ExcelProperty(index = 1,value = "周会次数")
    @JSONField(name = "weeklyMeetingCount")
    private String weeklyMeetingCount;
    @ExcelProperty(index = 2,value = "月会次数")
    @JSONField(name = "monthlyMeetingCount")
    private String monthlyMeetingCount;
    @ExcelProperty(index = 3,value = "其他会议次数")
    @JSONField(name = "otherMeetingCount")
    private String otherMeetingCount;
    @ExcelProperty(index = 4,value = "合计会议次数")
    @JSONField(name = "allMeetingCount")
    private String allMeetingCount;   //null
    @ExcelProperty(index = 5,value = "交流次数")
    @JSONField(name = "teamActivityCount")
    private String teamActivityCount;
    @ExcelProperty(index = 6,value = "团建次数")
    @JSONField(name = "exchangeActivityCount")
    private int exchangeActivityCount;
    @ExcelProperty(index = 7,value = "异业活动次数")
    @JSONField(name = "heterodoxyActivityCount")
    private int heterodoxyActivityCount;
    @ExcelProperty(index = 8,value = "游学次数")
    @JSONField(name = "studyTourActivityCount")
    private int studyTourActivityCount;
    @ExcelProperty(index = 9,value = "其他活动次数")
    @JSONField(name = "otherActivityCount")
    private int otherActivityCount;
    @ExcelProperty(index = 10,value = "合计活动次数")
    @JSONField(name = "allActivityCount")
    private int allActivityCount;
}
