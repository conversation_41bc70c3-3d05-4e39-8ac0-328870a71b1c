package com.jsunicom.oms.filter;


import com.alibaba.fastjson.JSON;
import com.jsunicom.common.core.entity.user.UserInfo;
import com.jsunicom.oms.mapper.UserRoleInfoMapper;
import com.jsunicom.oms.po.UserRoleInfo;
import com.jsunicom.oms.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.http.MimeHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> zhaowang
 * @ClassName TokenAuthorFilter
 * @Description TODO 自定义过滤器
 * @date : 2021/12/23 上午11:36
 * @Version 1.0
 **/
@Slf4j
@Component
@Order(1)
@ConfigurationProperties(prefix = "leyou.filter")
public class TokenAuthorFilter implements Filter {

    private static final String ALLOWED_HEADERS = "Origin, X-Requested-With, Content-Type, Accept, authorization, Authorization, credential, X-XSRF-TOKEN,token,username,client";
    private static final String ALLOWED_METHODS = "*";
    private static final String ALLOWED_ORIGIN = "*";
    private static final String MAX_AGE = "3600";
    @Autowired
    private UserRoleInfoMapper userRoleInfoMapper;

    /**
     * 需要忽略的地址
     */
//    private static String[] ignores = new String[]{
//        "/campus/toWoPage",   //下单
//    };
    //功能请求白名单
    private String[] allowPaths = new String[0];

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
        log.info("进入过滤器 start... ...");
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        //判断url是否需要拦截
        log.info(request.getRequestURI());
//        if (this.ignoring(request.getRequestURI())) {
//            chain.doFilter(request, response);
//            return;
//        }

        //设置允许跨域的配置
        // 这里填写你允许进行跨域的主机ip（正式上线时可以动态配置具体允许的域名和IP）
        response.setHeader("Access-Control-Allow-Origin", ALLOWED_ORIGIN);
        // 允许的访问方法
        response.setHeader("Access-Control-Allow-Methods",ALLOWED_METHODS);
        // Access-Control-Max-Age 用于 CORS 相关配置的缓存
        response.setHeader("Access-Control-Max-Age", MAX_AGE);
        response.setHeader("Access-Control-Allow-Headers",ALLOWED_HEADERS);


        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");

//        String sessionId = request.getHeader("sessionid");
//        log.info("=====checkLoginUserH5:",sessionId);
//        // 从头文件中获取cookie信息
//        Cookie[] cookies = request.getCookies();
//        //有无标志访问的Cookie
//        if (cookies!=null){
//            for (Cookie cookie:cookies){
//                if ("sessionid".equals(cookie.getName())){
//                    String jsessionid =  cookie.getValue();
//                    log.info("=====sessionId:",jsessionid);
//                }
//            }
//        }
        // 从头文件中获取cookie信息
        Cookie[] cookies = request.getCookies();
        if (cookies!=null){
            for (Cookie cookie:cookies){
                if ("sessionid".equals(cookie.getName())){
                    String jsessionid =  cookie.getValue();
                    Map<String,String> map=new HashMap<>();
                    map.put("Session_id",jsessionid);
                    addHeader(request,map);
                }
            }
        }

        // TODO 开发阶段，先默认一个userInfoBase64值，上环境时需要注释
//        String userInfoBase64 = "eyJhcmVhQ29kZSI6IiIsIm5jU2VyaWFsTnVtYmVyIjoiIiwic3RhZmZObyI6IlowMDAwRFdMIiwicHJvdmluY2UiOiIiLCJzdGFmZkNsYXNzIjoiIiwic2V4IjoiIiwic3RhZmZOYW1lIjoi5p2c5Y2r5YipIiwidXNlclBpZCI6IiIsImRlcGFydENvZGUiOiIiLCJkaW1pc3Npb25UYWciOiIiLCJkZXBhcnRLaW5kVHlwZSI6IiIsImRlcGFydE9yQ2hubE5hbWUiOiIifQ==";
        //非省份管理员
//          String userInfoBase64 = "eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDIyIiwic3RhZmZObyI6InRlc3RfZXBhcmNoeSIsInN0YWZmTmFtZSI6IuWcsOW4gueuoeeQhuWRmCIsInNleCI6IjEiLCJzZXJpYWxOdW1iZXIiOiIxNTAxMDA3MjIyMiIsIm5jU2VyaWFsTnVtYmVyIjoiMTUwMTAwNzIyMjIiLCJwcm92aW5jZSI6Ijg0IiwiZGVwYXJ0T3JDaG5sTmFtZSI6Iuilv+WuieW4guWIhuWFrOWPuCIsImRpbWlzc2lvblRhZyI6IjAiLCJkZXBhcnRLaW5kVHlwZSI6IjMiLCJzdGFmZkNsYXNzIjoiMSIsImRlcGFydENvZGUiOiI0MTIzNDIiLCJhcmVhQ29kZSI6Ijg0MSJ9";
        //省份管理员
        String userInfoBase64 = "eyJ1c2VyUGlkIjoiNTEwMTAyMTk3MjAzMjU4NDkxIiwic3RhZmZObyI6InRlc3RfcHJvdmluY2UiLCJzdGFmZk5hbWUiOiLnnIHnrqHnkIblkZgiLCJzZXgiOiIxIiwic2VyaWFsTnVtYmVyIjoiMTUwMTAwNzg4ODgiLCJuY1NlcmlhbE51bWJlciI6IjE1MDEwMDc4ODg4IiwicHJvdmluY2UiOiI4NCIsImRlcGFydE9yQ2hubE5hbWUiOiLpmZXopb/nnIHliIblhazlj7jkv6Hmga/ljJbpg6giLCJkaW1pc3Npb25UYWciOiIwIiwiZGVwYXJ0S2luZFR5cGUiOiI0Iiwic3RhZmZDbGFzcyI6IjEiLCJkZXBhcnRDb2RlIjoiMzQxMDI1MSIsImFyZWFDb2RlIjoiIn0=";
       // String userInfoBase64 = request.getHeader("User-Info");
        log.info("User-Info：{}",userInfoBase64);

        String resultCode = "0000";
        String resultDesc = "";
        boolean isFilter = true;

        String method = ((HttpServletRequest) request).getMethod();
        if (method.equals("OPTIONS")) {
            response.setStatus(HttpServletResponse.SC_OK);
        }else{
            if (!StringUtils.isBlank(userInfoBase64)) {
                try {
                    UserInfo userInfo = Utils.getUserInfo(userInfoBase64);
                    log.info("userInfo：{}", JSON.toJSONString(userInfo));
                    /*Map<String,String> map=new HashMap<>();
                    map.put("staffNo",userInfo.getStaffNo());
                    map.put("staffClass",userInfo.getStaffClass());
                    map.put("areaCode",provinceAreaCode(userInfo.getAreaCode()));
                    map.put("departCode",userInfo.getDepartCode());
                    map.put("serialNumber",userInfo.getSerialNumber());
                    map.put("staffName",userInfo.getStaffName());
                    map.put("userPid",userInfo.getUserPid());
                    addHeader(request,map);*/
                    UserRoleInfo userRoleInfo1 = userRoleInfoMapper.selectByPrimaryKey(userInfo.getUserPid(), userInfo.getStaffNo());
                    if (userRoleInfo1==null){
                        //插入
                        UserRoleInfo userRoleInfo=new UserRoleInfo();
                        userRoleInfo.setUserPid(userInfo.getUserPid()==null?System.currentTimeMillis()+"":userInfo.getUserPid());
                        userRoleInfo.setStaffNo(userInfo.getStaffNo());
                        userRoleInfo.setStaffName(userInfo.getStaffName());
                        userRoleInfo.setSerialNumber(userInfo.getSerialNumber());
                        if (StringUtils.isEmpty(userInfo.getAreaCode())){
                            userRoleInfo.setAreaLevel("1");
                        }else {
                            userRoleInfo.setAreaLevel("2");
                        }
                        if ("0".equals(userInfo.getDimissionTag())){
                            userRoleInfo.setState("0");
                        }else {
                            userRoleInfo.setState("1");
                        }
                        userRoleInfo.setAreaCode(provinceAreaCode(userInfo.getAreaCode()));
                        userRoleInfo.setRoleCode("3");
                        userRoleInfo.setRoleName("校区经理");
                        userRoleInfo.setCreateTime(new Date());
                        userRoleInfoMapper.insert(userRoleInfo);
                        Map<String,String> map=new HashMap<>();
                        map.put("staffNo",userInfo.getStaffNo());
                        map.put("staffClass",userInfo.getStaffClass());
                        map.put("areaCode",provinceAreaCode(userInfo.getAreaCode()));
                        map.put("departCode",userInfo.getDepartCode());
                        map.put("serialNumber",userInfo.getSerialNumber());
                        map.put("staffName",userInfo.getStaffName());
                        map.put("userPid",userInfo.getUserPid());
                        map.put("roleCode",userRoleInfo.getRoleCode());
                        addHeader(request,map);
                    }else {
                        Map<String,String> map=new HashMap<>();
                        map.put("staffNo",userRoleInfo1.getStaffNo());
                        map.put("staffClass",userInfo.getStaffClass());
                        map.put("areaCode",userRoleInfo1.getAreaCode());
                        map.put("departCode",userInfo.getDepartCode());
                        map.put("serialNumber",userRoleInfo1.getSerialNumber());
                        map.put("staffName",userRoleInfo1.getStaffName());
                        map.put("userPid",userRoleInfo1.getUserPid());
                        map.put("roleCode",userRoleInfo1.getRoleCode());
                        addHeader(request,map);
                    }
                }catch (Exception e){
                    chain.doFilter(request, response);
                    resultCode = "9999";
                    resultDesc = "用户授权认证没有通过!userInfoBase64解析失败！";
                }
            }else{
                chain.doFilter(request, response);
                resultCode = "9999";
                resultDesc = "";
            }

            if (!resultCode.equals("0000")) {// 验证失败
                PrintWriter writer = null;
                OutputStreamWriter osw = null;
                try {
                    osw = new OutputStreamWriter(response.getOutputStream(),"UTF-8");
                    writer = new PrintWriter(osw, true);
                    writer.write(resultDesc);
                    writer.flush();
                    writer.close();
                    osw.close();
                } catch (UnsupportedEncodingException e) {
                    log.error("过滤器返回信息失败:" + e.getMessage(), e);
                } catch (IOException e) {
                    log.error("过滤器返回信息失败:" + e.getMessage(), e);
                } finally {
                    if (null != writer) {
                        writer.close();
                    }
                    if (null != osw) {
                        osw.close();
                    }
                }
                return;
            }
            if (isFilter) {
                log.info("token filter过滤ok!");
                chain.doFilter(request, response);
            }
        }
    }

    /**
     * 判断url是否需要拦截
     * @param uri
     * @return
     */
    private boolean ignoring(String uri) {
        String allowPaths[] = this.getAllowPaths();
        for (String string : allowPaths) {
            if (uri.contains(string) && !uri.contains("/campus/modify") && !uri.contains("/campus/uploadCampusSave") && !uri.contains("/campus/queryByCondition")) {
                return true;
            }
        }
        return false;
    }

    private void addHeader(HttpServletRequest request, Map<String, String> headerMap) {
        if (headerMap==null||headerMap.isEmpty()){
            return;
        }

        Class<? extends HttpServletRequest> c=request.getClass();
        //System.out.println(c.getName());
        log.info("request实现类="+c.getName());
        try{
            Field requestField=c.getDeclaredField("request");
            requestField.setAccessible(true);

            Object o=requestField.get(request);
            Field coyoteRequest=o.getClass().getDeclaredField("coyoteRequest");
            coyoteRequest.setAccessible(true);

            Object o2=coyoteRequest.get(o);
            log.info("coyoteRequest实现类="+o2.getClass().getName());
            Field headers=o2.getClass().getDeclaredField("headers");
            headers.setAccessible(true);

            MimeHeaders mimeHeaders=(MimeHeaders) headers.get(o2);
            for (Map.Entry<String,String> entry:headerMap.entrySet()){
                mimeHeaders.removeHeader(entry.getKey());
                mimeHeaders.addValue(entry.getKey()).setString(entry.getValue());
            }

        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public String provinceAreaCode(String zbAreaCode){
        switch (zbAreaCode)
        {

            case "841":
                return "0029";
            case "844":
                return "0910";
            case "842":
                return "0911";
            case "845":
                return "0912";
            case "843":
                return "0913";
            case "847":
                return "0914";
            case "848":
                return "0915";
            case "849":
                return "0916";
            case "840":
                return "0917";
            case "846":
                return "0919";
            default:
                return "";
        }
    }

    public String[] getAllowPaths() {
        return allowPaths;
    }

    public void setAllowPaths(String[] allowPaths) {
        this.allowPaths = allowPaths;
    }
}
