package com.jsunicom.oms.utils;

import com.jsunicom.oms.request.HeadQuartersPostRequestHead;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: yjh
 * @Version: V1.00
 * @Date: Created in  2022/4/13 16:51
 * @Since: V1.00
 */
@Component
public class HeadAbilityTools {

    //总部能开报文头timestamp格式化类型
    SimpleDateFormat timeStampHeadQuartersFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss SSS");

    //总部能开报文头流水号格式化类型
    SimpleDateFormat transIdHeadQuartersFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");

    @Value("${province.trans-sys-code:3400}")
    private String sysCode;

    /**
     * 创建总部请求报文头
     *
     * @param appId
     * @param appToken
     * @return
     * @throws Exception
     */
    public HeadQuartersPostRequestHead createHeadQuartersPostRequestHead(String appId, String appToken) throws Exception {
        HeadQuartersPostRequestHead requestHead = new HeadQuartersPostRequestHead();
        requestHead.setAppId(appId);
        String timeStamp = timeStampHeadQuartersFormat.format(new Date());
        requestHead.setTimeStamp(timeStamp);
        String transId = getTransId(sysCode);
        requestHead.setTransId(transId);
        Map<String, String> headMap = new HashMap<String, String>(3);
        headMap.put("APP_ID", appId);
        headMap.put("TIMESTAMP", timeStamp);
        headMap.put("TRANS_ID", transId);
        String token = createHeadQuartersToken(headMap, appToken);
        requestHead.setToken(token);
        return requestHead;
    }


    /**
     * 创建能开请求流水
     *
     * @param sysCode
     * @return
     */
    private String getTransId(String sysCode) {
        int randInt = 0;
        StringBuffer sb = new StringBuffer();
        sb.append(transIdHeadQuartersFormat.format(new Date()));
        try {
            Random rnd = new Random();
            randInt = rnd.nextInt(1000000);
            sb.append(randInt);
        } catch (Exception ex) {

        }
        return sb.toString();
    }

    /**
     * 总部能开post请求 token创建
     *
     * @param requestMap
     * @param appToken
     * @return
     * @throws Exception
     */
    private String createHeadQuartersToken(Map<String, String> requestMap, String appToken) throws Exception {

        StringBuffer tokenBuffer = new StringBuffer();
        if (null != requestMap && !requestMap.isEmpty()) {
            List<String> keys = new ArrayList<String>(requestMap.keySet());
            Collections.sort(keys);
            for (String key : keys) {
                //System.out.println(key);
                tokenBuffer.append(key);
                tokenBuffer.append(requestMap.get(key));
            }
        }
        tokenBuffer.append(appToken);
        String token = "";
        MessageDigest digest = null;
        try {
            digest = MessageDigest.getInstance("md5");
            byte[] result = digest.digest(tokenBuffer.toString().getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : result) {
                int number = b & 0xff;
                String hex = Integer.toHexString(number);
                if (hex.length() == 1) {
                    sb.append("0" + hex);
                } else {
                    sb.append(hex);
                }
            }
            token = sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new Exception("加密md5失败:" + e.getMessage());
        }
        return token;
    }


    /**
     * 创建总部请求报文头
     *
     * @param isGzip
     * @return
     * @throws Exception
     */
    public HashMap<String, String> createHeadQuartersHeadRequestMap(boolean isGzip) throws Exception {
        HashMap<String, String> headMap = new HashMap<String, String>(4);
        headMap.put("Content-Type", "application/json; charset=UTF-8");
        headMap.put("Accept", "application/json");
        if (isGzip) {
            headMap.put("Accept-Encoding", "gzip");
        } else {
            headMap.put("Accept-Encoding", "");
        }
        return headMap;
    }



}
