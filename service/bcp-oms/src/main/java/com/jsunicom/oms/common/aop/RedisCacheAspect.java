package com.jsunicom.oms.common.aop;

import com.jsunicom.oms.common.annotation.RedisHashCache;
import com.jsunicom.oms.utils.RedisUtil;
import com.lz.lsf.exception.BusinessException;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;

import java.lang.reflect.Method;

/**
 * RedisCacheAspect。使用Redis 哈希(Hash)缓存目标方法返回的内容。
 * 
 * <AUTHOR>
 *
 */
@Component
@Aspect
public class RedisCacheAspect {
	@Autowired
	private RedisUtil redisUtils;

	private static String CONNECTOR = "_";

	/**
	 * 切入点
	 * */
	@Pointcut("@annotation(com.jsunicom.oms.common.annotation.RedisHashCache) ")
 	public void entryPoint() {
		// 无需内容
	}

	/**
	 * 解析RedisHashCache自定义注解。先取缓存内容，如果缓存有值，则返回缓存中的值，否则执行目标方法，并将返回的值保存至缓存。
	 * 
	 * @param proceedingJoinPoint
	 * @return
	 * @throws Exception
	 */
	@Around("entryPoint()")
	public Object around(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
		Object methodReturnValue = null;
		try {
			Signature signature = proceedingJoinPoint.getSignature(); // 获取连接点的方法签名对象
			if (signature instanceof MethodSignature) {
				MethodSignature methodSignature = (MethodSignature) signature;
				Method aopMethod = methodSignature.getMethod(); // 获取方法对象

				RedisHashCache cacheParam = aopMethod.getAnnotation(RedisHashCache.class);
				if (cacheParam != null) {
					String redisKey = cacheParam.key();
					String fieldKey = this.buildField(aopMethod.getName(), proceedingJoinPoint.getArgs(),
							cacheParam.fieldIndex()); // 拼接Redis哈希(Hash)的fieldKey
					if (StringUtils.isNotBlank(fieldKey)) {
//						Object cachedResult = redisService.hget(redisKey, fieldKey, cacheParam.type(),cacheParam.isArray()); // 获取redis缓存内容
						Object cachedResult = redisUtils.hget(redisKey, fieldKey, cacheParam.type(),cacheParam.isArray()); // 获取redis缓存内容
						if (null == cachedResult) { // 无缓存内容
							methodReturnValue = proceedingJoinPoint.proceed(); // 调用目标方法，获取返回值
							if (null != methodReturnValue) {
								// 返回值不等于空值才写入redis
								redisUtils.hset(redisKey, fieldKey, methodReturnValue, cacheParam.expire());
							}
						} else { // 有缓存内容
							methodReturnValue = cachedResult; // 返回缓存中的内容
						}
					} else { // 获取Redis 哈希(Hash)的fieldKey为空，直接调用目标方法，不缓存。
						methodReturnValue = proceedingJoinPoint.proceed();
					}
				} else { // 方法上无RedisCache注解，直接调用目标方法，不缓存。
					methodReturnValue = proceedingJoinPoint.proceed();
				}
			} else { // 连接点的方法签名对象为空，直接调用目标方法，不缓存。
				methodReturnValue = proceedingJoinPoint.proceed();
			}
		} catch (Exception e) {
			throw e;
		}
		return methodReturnValue;
	}

	/**
	 * <p>
	 * 拼接方法的入参，作为 redis hash 的fieldKey。 比如：
	 * </p>
	 * 
	 * <pre>
	 * 方法定义：public Object fun(String str1, int num){}
	 * </p>
	 * 
	 * <pre>
	 * 示例1：fieldIndex = {0, 1} 方法入参为：("测试", 20) 则fieldKey为：测试_20
	 * 方法入参为：("helloworld", 4) 则fieldKey为：helloworld_4 方法入参为：(null, 4)
	 * 则fieldKey为：n-u-l-l_4
	 * </p>
	 * 
	 * <pre>
	 * 示例2：fieldIndex = {0} 方法入参为：("测试", 20) 则fieldKey为：测试 方法入参为：("helloworld", 4)
	 * 则fieldKey为：helloworld 方法入参为：(null, 4) 则fieldKey为：n-u-l-l
	 * </p>
	 * 
	 * @param args
	 * @param fieldIndex
	 * @return
	 */
	private String buildField(String methodName, Object[] args, int[] fieldIndex) {
		int argsLen = ArrayUtils.getLength(args);
		if (argsLen <= 0) { // 如果方法无参数，则以方法名作为Redis 哈希(Hash)的fieldKey
			return methodName;
		}

		StringBuilder fieldKey = new StringBuilder();
		int fieldIndexLen = ArrayUtils.getLength(fieldIndex);
		if (fieldIndexLen > 0) {
			for (int i = 0; i < fieldIndexLen; i++) {
				if (fieldIndex[i] >= argsLen) {
					throw new BusinessException("",
							"非法的redis hash field。期望参数index为[" + fieldIndex[i] + "]，实际参数个数为[" + argsLen + "]");
				}
				Object arg = args[fieldIndex[i]];
				if (null == arg) {
					fieldKey.append("n-u-l-l").append(CONNECTOR);
				} else {
					if (ClassUtils.isPrimitiveOrWrapper(arg.getClass()) || arg instanceof String) {
						String argStr = arg.toString();
						fieldKey.append(StringUtils.isBlank(argStr) ? "b-l-a-n-k" : argStr).append(CONNECTOR);
					} else {
						throw new BusinessException("", "非法的redis hash field。Field 只能为 基本类型或String");
					}
				}
			}
			fieldKey.deleteCharAt(fieldKey.length() - 1);
		}
		return fieldKey.toString();
	}

}
