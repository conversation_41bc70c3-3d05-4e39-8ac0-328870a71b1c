package com.jsunicom.oms.service.report;

import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.dto.report.CampusChannelReportDto;
import com.jsunicom.oms.dto.report.CampusChannelReportParam;
import com.jsunicom.oms.dto.report.ExtendReportDto;
import com.jsunicom.oms.dto.report.ReportDto;
import com.jsunicom.oms.model.partner.Partner;
import com.jsunicom.oms.model.woshcool.WoSchoolParamDto;
import com.jsunicom.oms.po.UserRoleInfo;

import java.util.List;
import java.util.Map;

public interface ReportService {
    List<Map<String, Object>> meettingAndActivity(WoSchoolParamDto paramDto);

    List<Map<String,Object>> taskCompletionRate(String serialNumber,WoSchoolParamDto paramDto);

    List<ReportDto> queryCompletionRateByEarpchy(String serialNumber, Map<String,Object> map);

    List<ExtendReportDto> queryCompletionRateByPartner(String serialNumber, Map<String,Object> map);

    UserRoleInfo queryUserRoleInfo(String acctNo);

    Partner queryPartner(String acctNo);

    /**
     * 查询校园渠道发展量报表
     * @param param 查询参数
     * @return 分页结果
     */
    PageInfo<CampusChannelReportDto> queryCampusChannelReport(CampusChannelReportParam param);
}
