package com.jsunicom.oms.controller.report;

import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.common.result.ResultUtil;
import com.jsunicom.oms.dto.report.CampusChannelReportDto;
import com.jsunicom.oms.dto.report.CampusChannelReportParam;
import com.jsunicom.oms.service.report.ReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 6/5/2025 3:25 PM
 */
@Slf4j
@RestController
@RequestMapping(value = "/campusChannel")
public class CampusChannelReportController {

    @PostMapping("/test")
    public CustomResult test() {
        return ResultUtil.success("测试成功");
    }
}
