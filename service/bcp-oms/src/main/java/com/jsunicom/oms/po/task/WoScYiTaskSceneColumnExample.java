package com.jsunicom.oms.po.task;

import java.util.ArrayList;
import java.util.List;

public class WoScYiTaskSceneColumnExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WoScYiTaskSceneColumnExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andTaskSceneTypeCodeIsNull() {
            addCriterion("TASK_SCENE_TYPE_CODE is null");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeIsNotNull() {
            addCriterion("TASK_SCENE_TYPE_CODE is not null");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeEqualTo(String value) {
            addCriterion("TASK_SCENE_TYPE_CODE =", value, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeNotEqualTo(String value) {
            addCriterion("TASK_SCENE_TYPE_CODE <>", value, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeGreaterThan(String value) {
            addCriterion("TASK_SCENE_TYPE_CODE >", value, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("TASK_SCENE_TYPE_CODE >=", value, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeLessThan(String value) {
            addCriterion("TASK_SCENE_TYPE_CODE <", value, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeLessThanOrEqualTo(String value) {
            addCriterion("TASK_SCENE_TYPE_CODE <=", value, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeLike(String value) {
            addCriterion("TASK_SCENE_TYPE_CODE like", value, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeNotLike(String value) {
            addCriterion("TASK_SCENE_TYPE_CODE not like", value, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeIn(List<String> values) {
            addCriterion("TASK_SCENE_TYPE_CODE in", values, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeNotIn(List<String> values) {
            addCriterion("TASK_SCENE_TYPE_CODE not in", values, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeBetween(String value1, String value2) {
            addCriterion("TASK_SCENE_TYPE_CODE between", value1, value2, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andTaskSceneTypeCodeNotBetween(String value1, String value2) {
            addCriterion("TASK_SCENE_TYPE_CODE not between", value1, value2, "taskSceneTypeCode");
            return (Criteria) this;
        }

        public Criteria andColumnAnameIsNull() {
            addCriterion("COLUMN_ANAME is null");
            return (Criteria) this;
        }

        public Criteria andColumnAnameIsNotNull() {
            addCriterion("COLUMN_ANAME is not null");
            return (Criteria) this;
        }

        public Criteria andColumnAnameEqualTo(String value) {
            addCriterion("COLUMN_ANAME =", value, "columnAname");
            return (Criteria) this;
        }

        public Criteria andColumnAnameNotEqualTo(String value) {
            addCriterion("COLUMN_ANAME <>", value, "columnAname");
            return (Criteria) this;
        }

        public Criteria andColumnAnameGreaterThan(String value) {
            addCriterion("COLUMN_ANAME >", value, "columnAname");
            return (Criteria) this;
        }

        public Criteria andColumnAnameGreaterThanOrEqualTo(String value) {
            addCriterion("COLUMN_ANAME >=", value, "columnAname");
            return (Criteria) this;
        }

        public Criteria andColumnAnameLessThan(String value) {
            addCriterion("COLUMN_ANAME <", value, "columnAname");
            return (Criteria) this;
        }

        public Criteria andColumnAnameLessThanOrEqualTo(String value) {
            addCriterion("COLUMN_ANAME <=", value, "columnAname");
            return (Criteria) this;
        }

        public Criteria andColumnAnameLike(String value) {
            addCriterion("COLUMN_ANAME like", value, "columnAname");
            return (Criteria) this;
        }

        public Criteria andColumnAnameNotLike(String value) {
            addCriterion("COLUMN_ANAME not like", value, "columnAname");
            return (Criteria) this;
        }

        public Criteria andColumnAnameIn(List<String> values) {
            addCriterion("COLUMN_ANAME in", values, "columnAname");
            return (Criteria) this;
        }

        public Criteria andColumnAnameNotIn(List<String> values) {
            addCriterion("COLUMN_ANAME not in", values, "columnAname");
            return (Criteria) this;
        }

        public Criteria andColumnAnameBetween(String value1, String value2) {
            addCriterion("COLUMN_ANAME between", value1, value2, "columnAname");
            return (Criteria) this;
        }

        public Criteria andColumnAnameNotBetween(String value1, String value2) {
            addCriterion("COLUMN_ANAME not between", value1, value2, "columnAname");
            return (Criteria) this;
        }

        public Criteria andColumnNameIsNull() {
            addCriterion("COLUMN_NAME is null");
            return (Criteria) this;
        }

        public Criteria andColumnNameIsNotNull() {
            addCriterion("COLUMN_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andColumnNameEqualTo(String value) {
            addCriterion("COLUMN_NAME =", value, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameNotEqualTo(String value) {
            addCriterion("COLUMN_NAME <>", value, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameGreaterThan(String value) {
            addCriterion("COLUMN_NAME >", value, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameGreaterThanOrEqualTo(String value) {
            addCriterion("COLUMN_NAME >=", value, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameLessThan(String value) {
            addCriterion("COLUMN_NAME <", value, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameLessThanOrEqualTo(String value) {
            addCriterion("COLUMN_NAME <=", value, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameLike(String value) {
            addCriterion("COLUMN_NAME like", value, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameNotLike(String value) {
            addCriterion("COLUMN_NAME not like", value, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameIn(List<String> values) {
            addCriterion("COLUMN_NAME in", values, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameNotIn(List<String> values) {
            addCriterion("COLUMN_NAME not in", values, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameBetween(String value1, String value2) {
            addCriterion("COLUMN_NAME between", value1, value2, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameNotBetween(String value1, String value2) {
            addCriterion("COLUMN_NAME not between", value1, value2, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnCnameIsNull() {
            addCriterion("COLUMN_CNAME is null");
            return (Criteria) this;
        }

        public Criteria andColumnCnameIsNotNull() {
            addCriterion("COLUMN_CNAME is not null");
            return (Criteria) this;
        }

        public Criteria andColumnCnameEqualTo(String value) {
            addCriterion("COLUMN_CNAME =", value, "columnCname");
            return (Criteria) this;
        }

        public Criteria andColumnCnameNotEqualTo(String value) {
            addCriterion("COLUMN_CNAME <>", value, "columnCname");
            return (Criteria) this;
        }

        public Criteria andColumnCnameGreaterThan(String value) {
            addCriterion("COLUMN_CNAME >", value, "columnCname");
            return (Criteria) this;
        }

        public Criteria andColumnCnameGreaterThanOrEqualTo(String value) {
            addCriterion("COLUMN_CNAME >=", value, "columnCname");
            return (Criteria) this;
        }

        public Criteria andColumnCnameLessThan(String value) {
            addCriterion("COLUMN_CNAME <", value, "columnCname");
            return (Criteria) this;
        }

        public Criteria andColumnCnameLessThanOrEqualTo(String value) {
            addCriterion("COLUMN_CNAME <=", value, "columnCname");
            return (Criteria) this;
        }

        public Criteria andColumnCnameLike(String value) {
            addCriterion("COLUMN_CNAME like", value, "columnCname");
            return (Criteria) this;
        }

        public Criteria andColumnCnameNotLike(String value) {
            addCriterion("COLUMN_CNAME not like", value, "columnCname");
            return (Criteria) this;
        }

        public Criteria andColumnCnameIn(List<String> values) {
            addCriterion("COLUMN_CNAME in", values, "columnCname");
            return (Criteria) this;
        }

        public Criteria andColumnCnameNotIn(List<String> values) {
            addCriterion("COLUMN_CNAME not in", values, "columnCname");
            return (Criteria) this;
        }

        public Criteria andColumnCnameBetween(String value1, String value2) {
            addCriterion("COLUMN_CNAME between", value1, value2, "columnCname");
            return (Criteria) this;
        }

        public Criteria andColumnCnameNotBetween(String value1, String value2) {
            addCriterion("COLUMN_CNAME not between", value1, value2, "columnCname");
            return (Criteria) this;
        }

        public Criteria andStateIsNull() {
            addCriterion("STATE is null");
            return (Criteria) this;
        }

        public Criteria andStateIsNotNull() {
            addCriterion("STATE is not null");
            return (Criteria) this;
        }

        public Criteria andStateEqualTo(String value) {
            addCriterion("STATE =", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotEqualTo(String value) {
            addCriterion("STATE <>", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThan(String value) {
            addCriterion("STATE >", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThanOrEqualTo(String value) {
            addCriterion("STATE >=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThan(String value) {
            addCriterion("STATE <", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThanOrEqualTo(String value) {
            addCriterion("STATE <=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLike(String value) {
            addCriterion("STATE like", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotLike(String value) {
            addCriterion("STATE not like", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateIn(List<String> values) {
            addCriterion("STATE in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotIn(List<String> values) {
            addCriterion("STATE not in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateBetween(String value1, String value2) {
            addCriterion("STATE between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotBetween(String value1, String value2) {
            addCriterion("STATE not between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("REMARK is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("REMARK is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("REMARK =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("REMARK <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("REMARK >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("REMARK >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("REMARK <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("REMARK <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("REMARK like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("REMARK not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("REMARK in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("REMARK not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("REMARK between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("REMARK not between", value1, value2, "remark");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}