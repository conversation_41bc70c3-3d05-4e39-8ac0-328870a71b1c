package com.jsunicom.oms.controller.report;

import com.jsunicom.oms.common.result.CustomResult;
import com.jsunicom.oms.common.result.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 6/5/2025 3:25 PM
 */
@Slf4j
@RestController
@RequestMapping(value = "/campusChannel")
public class CampusChannelController {

    @PostMapping("/test")
    public CustomResult test() {
        return ResultUtil.success("测试成功");
    }
}
