package com.jsunicom.oms.mapper.report;

import com.jsunicom.oms.config.DS;
import com.jsunicom.oms.dto.report.CampusChannelReportDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 校园渠道发展量报表 - 达梦数据库专用Mapper
 * <AUTHOR>
 * @date 6/5/2025 4:00 PM
 */
public interface CampusChannelDmMapper {

    /**
     * 查询校园渠道发展量报表 - 达梦数据库
     * @param paramMap 查询参数
     * @return 校园渠道发展量列表
     */
    @DS("cbss")
    List<CampusChannelReportDto> queryCampusChannelReportFromDm(Map<String,Object> paramMap);
}
