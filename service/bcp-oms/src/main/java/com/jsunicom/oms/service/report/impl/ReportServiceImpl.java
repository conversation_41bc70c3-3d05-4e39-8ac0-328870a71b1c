package com.jsunicom.oms.service.report.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jsunicom.oms.config.DS;
import com.jsunicom.oms.dto.report.CampusChannelReportDto;
import com.jsunicom.oms.dto.report.CampusChannelReportParam;
import com.jsunicom.oms.dto.report.ExtendReportDto;
import com.jsunicom.oms.dto.report.ReportDto;
import com.jsunicom.oms.mapper.ReportMapper;
import com.jsunicom.oms.mapper.UserRoleInfoMapper;
import com.jsunicom.oms.mapper.partner.PartnerDao;
import com.jsunicom.oms.mapper.report.CampusChannelDmMapper;
import com.jsunicom.oms.model.partner.Partner;
import com.jsunicom.oms.model.woshcool.WoSchoolParamDto;
import com.jsunicom.oms.po.UserRoleInfo;
import com.jsunicom.oms.po.UserRoleInfoExample;
import com.jsunicom.oms.service.report.ReportService;
import com.jsunicom.oms.utils.BeanMapConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ReportServiceImpl implements ReportService {
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private UserRoleInfoMapper userRoleInfoMapper;
    @Autowired
    private PartnerDao partnerDao;
    @Autowired
    private CampusChannelDmMapper campusChannelDmMapper;
    @Override
    public List<Map<String, Object>> meettingAndActivity(WoSchoolParamDto paramDto){
        List<Map<String, Object>> maps = reportMapper.queryMeettingAndActivity(paramDto);
        int weeklyMeetingCount = maps.stream().mapToInt(map -> Integer.valueOf(map.get("weeklyMeetingCount").toString())).sum();
        int monthlyMeetingCount = maps.stream().mapToInt(map -> Integer.valueOf(map.get("monthlyMeetingCount").toString())).sum();
        int otherMeetingCount = maps.stream().mapToInt(map -> Integer.valueOf(map.get("otherMeetingCount").toString())).sum();
        int allMeetingCount = maps.stream().mapToInt(map -> Integer.valueOf(map.get("allMeetingCount").toString())).sum();
        int teamActivityCount = maps.stream().mapToInt(map -> Integer.valueOf(map.get("teamActivityCount").toString())).sum();
        int exchangeActivityCount = maps.stream().mapToInt(map -> Integer.valueOf(map.get("exchangeActivityCount").toString())).sum();
        int studyTourActivityCount = maps.stream().mapToInt(map -> Integer.valueOf(map.get("studyTourActivityCount").toString())).sum();
        int heterodoxyActivityCount = maps.stream().mapToInt(map -> Integer.valueOf(map.get("heterodoxyActivityCount").toString())).sum();
        int otherActivityCount = maps.stream().mapToInt(map -> Integer.valueOf(map.get("otherActivityCount").toString())).sum();
        int allActivityCount = maps.stream().mapToInt(map -> Integer.valueOf(map.get("allActivityCount").toString())).sum();
        Map<String, Object> map = new HashMap<>();
        map.put("orgName","总计");
        map.put("weeklyMeetingCount",weeklyMeetingCount);
        map.put("monthlyMeetingCount",monthlyMeetingCount);
        map.put("otherMeetingCount",otherMeetingCount);
        map.put("allMeetingCount",allMeetingCount);
        map.put("teamActivityCount",teamActivityCount);
        map.put("exchangeActivityCount",exchangeActivityCount);
        map.put("studyTourActivityCount",studyTourActivityCount);
        map.put("heterodoxyActivityCount",heterodoxyActivityCount);
        map.put("otherActivityCount",otherActivityCount);
        map.put("allActivityCount",allActivityCount);
        maps.add(map);
        return maps;
    }

    @Override
    public List<Map<String,Object>> taskCompletionRate(String serialNumber,WoSchoolParamDto paramDto){
        UserRoleInfo roleInfo = queryUserRoleInfo(serialNumber);
        //地市管理员 只查本地市下校区渠道录入、校区信息补充数量，相当于前端传了一个地市编码过来
        if(roleInfo.getRoleCode().equals("2")){
            paramDto.setEparchyCode(roleInfo.getAreaCode());
        }
        //校区经理 只查校区经理所管辖校区下已录入渠道、校区信息补充数量
        if(roleInfo.getRoleCode().equals("3")){
            Partner partner = queryPartner(serialNumber);
            paramDto.setId(partner.getId());
        }
        List<Map<String, Object>> maps = reportMapper.queryTaskCompletionRate(paramDto);
        return maps;
    }

    @Override
    public List<ReportDto> queryCompletionRateByEarpchy(String serialNumber,Map<String,Object> map){
        UserRoleInfo roleInfo = queryUserRoleInfo(serialNumber);
        //地市管理员 只查本地市下校区渠道录入、校区信息补充数量，相当于前端传了一个地市编码过来
        if(roleInfo.getRoleCode().equals("2")){
            map.put("eparchyCode",roleInfo.getAreaCode());
        }
        //校区经理 只查校区经理所管辖校区下已录入渠道、校区信息补充数量
        if(roleInfo.getRoleCode().equals("3")){
            map.put("acctNo",serialNumber);
        }
        List<ReportDto> maps = reportMapper.queryCompletionRateByEarpchy(map);
        return maps;
    }

    public List<ExtendReportDto> queryCompletionRateByPartner(String serialNumber,Map<String,Object> map){
        UserRoleInfo roleInfo = queryUserRoleInfo(serialNumber);
        //地市管理员 只查本地市下校区渠道录入、校区信息补充数量，相当于前端传了一个地市编码过来
        if(roleInfo.getRoleCode().equals("2")){
            map.put("eparchyCode",roleInfo.getAreaCode());
        }
        //校区经理 只查校区经理所管辖校区下已录入渠道、校区信息补充数量
        if(roleInfo.getRoleCode().equals("3")){
            map.put("acctNo",serialNumber);
        }
        List<ExtendReportDto> maps = reportMapper.queryExtendReport(map);
        return maps;
    }

    public UserRoleInfo queryUserRoleInfo(String acctNo){
        //判断操作员是否是校区经理
        UserRoleInfoExample example=new UserRoleInfoExample();
        UserRoleInfoExample.Criteria criteria = example.createCriteria();
        criteria.andSerialNumberEqualTo(acctNo);
        List<UserRoleInfo> userRoleInfos = userRoleInfoMapper.selectByExample(example);
        UserRoleInfo userRoleInfo = userRoleInfos.get(0);
        return userRoleInfo;
    }

    public Partner queryPartner(String acctNo){
        Partner partnerByMblNbr = partnerDao.findPartnerByMblNbr(acctNo);
        return partnerByMblNbr;
    }

    @DS("cbss")
    @Override
    public PageInfo<CampusChannelReportDto> queryCampusChannelReport(CampusChannelReportParam param) {
        // 设置分页参数
        PageHelper.startPage(param.getPageNumber(), param.getPageSize());

        // 转换参数为Map
        Map<String, Object> paramMap = BeanMapConvertUtils.bean2Map2(param);

        // 查询达梦数据库
        List<CampusChannelReportDto> list = campusChannelDmMapper.queryCampusChannelReportFromDm(paramMap);

        // 返回分页结果
        return new PageInfo<>(list);
    }

}
