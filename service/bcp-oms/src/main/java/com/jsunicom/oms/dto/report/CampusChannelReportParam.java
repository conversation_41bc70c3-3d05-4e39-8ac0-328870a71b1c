package com.jsunicom.oms.dto.report;

import com.jsunicom.oms.model.base.BasicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 校园渠道发展量报表查询参数
 * <AUTHOR>
 * @date 6/5/2025 3:30 PM
 */
@Data
@ApiModel("校园渠道发展量报表查询参数")
public class CampusChannelReportParam extends BasicModel {

    @ApiModelProperty("开始时间 格式：yyyy-MM-dd")
    private String startTime;

    @ApiModelProperty("结束时间 格式：yyyy-MM-dd")
    private String endTime;

    @ApiModelProperty("地市名称")
    private String eparchyName;

    @ApiModelProperty("地市编码")
    private String eparchyCode;

    @ApiModelProperty("页码，从1开始")
    private Integer pageNumber = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;
}
