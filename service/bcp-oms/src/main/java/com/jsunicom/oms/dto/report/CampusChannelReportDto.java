package com.jsunicom.oms.dto.report;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 校园渠道发展量报表响应DTO
 * <AUTHOR>
 * @date 6/5/2025 3:30 PM
 */
@Data
@ApiModel("校园渠道发展量报表响应DTO")
public class CampusChannelReportDto {

    @ApiModelProperty("地市名称")
    @JSONField(name = "tradeEparchyCode")
    @ExcelProperty(index = 0, value = "地市名称")
    private String tradeEparchyCode;

    @ApiModelProperty("渠道ID")
    @JSONField(name = "tradeDepartId")
    @ExcelProperty(index = 1, value = "渠道ID")
    private String tradeDepartId;

    @ApiModelProperty("融合产品发展量")
    @JSONField(name = "prodTypeCpCnt")
    @ExcelProperty(index = 2, value = "融合产品发展量")
    private Integer prodTypeCpCnt;

    @ApiModelProperty("4G产品发展量")
    @JSONField(name = "prodType40Cnt")
    @ExcelProperty(index = 3, value = "4G产品发展量")
    private Integer prodType40Cnt;

    @ApiModelProperty("5G产品发展量")
    @JSONField(name = "prodType50Cnt")
    @ExcelProperty(index = 4, value = "5G产品发展量")
    private Integer prodType50Cnt;

    @ApiModelProperty("总发展量")
    @JSONField(name = "totalCnt")
    @ExcelProperty(index = 5, value = "总发展量")
    private Integer totalCnt;

    public Integer getTotalCnt() {
        int cp = prodTypeCpCnt != null ? prodTypeCpCnt : 0;
        int type40 = prodType40Cnt != null ? prodType40Cnt : 0;
        int type50 = prodType50Cnt != null ? prodType50Cnt : 0;
        return cp + type40 + type50;
    }
}
