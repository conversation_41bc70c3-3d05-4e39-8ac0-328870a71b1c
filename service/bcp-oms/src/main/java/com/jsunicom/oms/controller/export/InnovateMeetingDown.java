package com.jsunicom.oms.controller.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class InnovateMeetingDown extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(index = 0,value = "地市")
    @JSONField(name = "orgName")
    private String orgName;
    @ExcelProperty(index = 1,value = "学校校区名称")
    @JSONField(name = "campusName")
    private String campusName;
    @ExcelProperty(index = 2,value = "青创社名称")
    @JSONField(name = "societyName")
    private String societyName;
    @ExcelProperty(index = 3,value = "会议标识")
    @JSONField(name = "meetId")
    private Long meetId;   //null
    @ExcelProperty(index = 4,value = "会议主题")
    @JSONField(name = "meetName")
    private String meetName;
    @ExcelProperty(index = 5,value = "会议类型")
    @JSONField(name = "meetType")
    private String meetType;
    @ExcelProperty(index = 6,value = "会议开始时间")
    @JSONField(name = "startTime")
    private String startTime;
    @ExcelProperty(index = 7,value = "会议结束时间")
    @JSONField(name = "endTime")
    private String endTime;
    @ExcelProperty(index = 8,value = "会议状态")
    @JSONField(name = "meetState")
    private String meetState;
    @ExcelProperty(index = 9,value = "流程状态")
    @JSONField(name = "orderState")
    private String orderState;
    @ExcelProperty(index = 10,value = "会议地点")
    @JSONField(name = "location")
    private String location;
    @ExcelProperty(index = 11,value = "会议内容")
    @JSONField(name = "content")
    private String content;
    @ExcelProperty(index = 12,value = "创建时间")
    @JSONField(name = "createdTime")
    private String createdTime;
    @ExcelProperty(index = 13,value = "创建人")
    @JSONField(name = "memberName")
    private String memberName;
    @ExcelProperty(index = 14,value = "手机号")
    @JSONField(name = "mblNbr")
    private String mblNbr;
}
