package com.jsunicom.oms.controller.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class ChannelDevelopReportDown extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(index = 0,value = "账期")
    @JSONField(name = "dayId")
    private String dayId;
    @ExcelProperty(index = 1,value = "地市")
    @JSONField(name = "orgName")
    private String orgName;
    @ExcelProperty(index = 2,value = "归属校区")
    @JSONField(name = "campusName")
    private String campusName;
    @ExcelProperty(index = 3,value = "归属校区经理")
    @JSONField(name = "partnerName")
    private String partnerName;
    @ExcelProperty(index = 4,value = "渠道ID")
    @JSONField(name = "channelId")
    private String channelId;
    @ExcelProperty(index = 5,value = "渠道名称")
    @JSONField(name = "channelName")
    private String channelName;
    @ExcelProperty(index = 6,value = "开户时间")
    @JSONField(name = "openTime")
    private String openTime;
    @ExcelProperty(index = 7,value = "业务号码（脱敏）")
    @JSONField(name = "deviceNumber")
    private String deviceNumber;
    @ExcelProperty(index = 8,value = "用户ID")
    @JSONField(name = "userId")
    private String userId;
    @ExcelProperty(index = 9,value = "是否在网")
    @JSONField(name = "removeFlagValue")
    private String removeFlagValue;
    @ExcelProperty(index = 10,value = "网别")
    @JSONField(name = "svcType")
    private String svcType;
}
