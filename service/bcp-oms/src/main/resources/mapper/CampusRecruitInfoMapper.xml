<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.CampusRecruitInfoMapper">
  <resultMap id="BaseResultMap" type="com.jsunicom.oms.po.CampusRecruitInfo">
    <id column="RECRUIT_ID" jdbcType="BIGINT" property="recruitId" />
    <result column="COLLEGE_ID" jdbcType="BIGINT" property="collegeId" />
    <result column="GRADE" jdbcType="VARCHAR" property="grade" />
    <result column="ENROLLMENT_NUMBER" jdbcType="INTEGER" property="enrollmentNumber" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="RESERVE1" jdbcType="VARCHAR" property="reserve1" />
    <result column="RESERVE2" jdbcType="VARCHAR" property="reserve2" />
    <result column="RESERVE3" jdbcType="VARCHAR" property="reserve3" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    RECRUIT_ID, COLLEGE_ID, GRADE, ENROLLMENT_NUMBER, CREATED_BY, CREATED_TIME, UPDATED_BY, 
    UPDATED_TIME, REMARK, RESERVE1, RESERVE2, RESERVE3
  </sql>
  <select id="selectByExample" parameterType="com.jsunicom.oms.po.CampusRecruitInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wo_school_campus_college_recruit_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wo_school_campus_college_recruit_info
    where RECRUIT_ID = #{recruitId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wo_school_campus_college_recruit_info
    where RECRUIT_ID = #{recruitId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jsunicom.oms.po.CampusRecruitInfoExample">
    delete from wo_school_campus_college_recruit_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jsunicom.oms.po.CampusRecruitInfo">
    insert into wo_school_campus_college_recruit_info (RECRUIT_ID, COLLEGE_ID, GRADE, 
      ENROLLMENT_NUMBER, CREATED_BY, CREATED_TIME, 
      UPDATED_BY, UPDATED_TIME, REMARK, 
      RESERVE1, RESERVE2, RESERVE3
      )
    values (#{recruitId,jdbcType=BIGINT}, #{collegeId,jdbcType=BIGINT}, #{grade,jdbcType=VARCHAR}, 
      #{enrollmentNumber,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{updatedBy,jdbcType=VARCHAR}, #{updatedTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, 
      #{reserve1,jdbcType=VARCHAR}, #{reserve2,jdbcType=VARCHAR}, #{reserve3,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.jsunicom.oms.po.CampusRecruitInfo">
    insert into wo_school_campus_college_recruit_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="recruitId != null">
        RECRUIT_ID,
      </if>
      <if test="collegeId != null">
        COLLEGE_ID,
      </if>
      <if test="grade != null">
        GRADE,
      </if>
      <if test="enrollmentNumber != null">
        ENROLLMENT_NUMBER,
      </if>
      <if test="createdBy != null">
        CREATED_BY,
      </if>
      <if test="createdTime != null">
        CREATED_TIME,
      </if>
      <if test="updatedBy != null">
        UPDATED_BY,
      </if>
      <if test="updatedTime != null">
        UPDATED_TIME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="reserve1 != null">
        RESERVE1,
      </if>
      <if test="reserve2 != null">
        RESERVE2,
      </if>
      <if test="reserve3 != null">
        RESERVE3,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="recruitId != null">
        #{recruitId,jdbcType=BIGINT},
      </if>
      <if test="collegeId != null">
        #{collegeId,jdbcType=BIGINT},
      </if>
      <if test="grade != null">
        #{grade,jdbcType=VARCHAR},
      </if>
      <if test="enrollmentNumber != null">
        #{enrollmentNumber,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="reserve1 != null">
        #{reserve1,jdbcType=VARCHAR},
      </if>
      <if test="reserve2 != null">
        #{reserve2,jdbcType=VARCHAR},
      </if>
      <if test="reserve3 != null">
        #{reserve3,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jsunicom.oms.po.CampusRecruitInfoExample" resultType="java.lang.Long">
    select count(*) from wo_school_campus_college_recruit_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wo_school_campus_college_recruit_info
    <set>
      <if test="record.recruitId != null">
        RECRUIT_ID = #{record.recruitId,jdbcType=BIGINT},
      </if>
      <if test="record.collegeId != null">
        COLLEGE_ID = #{record.collegeId,jdbcType=BIGINT},
      </if>
      <if test="record.grade != null">
        GRADE = #{record.grade,jdbcType=VARCHAR},
      </if>
      <if test="record.enrollmentNumber != null">
        ENROLLMENT_NUMBER = #{record.enrollmentNumber,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdTime != null">
        CREATED_TIME = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedTime != null">
        UPDATED_TIME = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null">
        REMARK = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.reserve1 != null">
        RESERVE1 = #{record.reserve1,jdbcType=VARCHAR},
      </if>
      <if test="record.reserve2 != null">
        RESERVE2 = #{record.reserve2,jdbcType=VARCHAR},
      </if>
      <if test="record.reserve3 != null">
        RESERVE3 = #{record.reserve3,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wo_school_campus_college_recruit_info
    set RECRUIT_ID = #{record.recruitId,jdbcType=BIGINT},
      COLLEGE_ID = #{record.collegeId,jdbcType=BIGINT},
      GRADE = #{record.grade,jdbcType=VARCHAR},
      ENROLLMENT_NUMBER = #{record.enrollmentNumber,jdbcType=INTEGER},
      CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
      CREATED_TIME = #{record.createdTime,jdbcType=TIMESTAMP},
      UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
      UPDATED_TIME = #{record.updatedTime,jdbcType=TIMESTAMP},
      REMARK = #{record.remark,jdbcType=VARCHAR},
      RESERVE1 = #{record.reserve1,jdbcType=VARCHAR},
      RESERVE2 = #{record.reserve2,jdbcType=VARCHAR},
      RESERVE3 = #{record.reserve3,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jsunicom.oms.po.CampusRecruitInfo">
    update wo_school_campus_college_recruit_info
    <set>
      <if test="collegeId != null">
        COLLEGE_ID = #{collegeId,jdbcType=BIGINT},
      </if>
      <if test="grade != null">
        GRADE = #{grade,jdbcType=VARCHAR},
      </if>
      <if test="enrollmentNumber != null">
        ENROLLMENT_NUMBER = #{enrollmentNumber,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        CREATED_TIME = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null">
        UPDATED_TIME = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="reserve1 != null">
        RESERVE1 = #{reserve1,jdbcType=VARCHAR},
      </if>
      <if test="reserve2 != null">
        RESERVE2 = #{reserve2,jdbcType=VARCHAR},
      </if>
      <if test="reserve3 != null">
        RESERVE3 = #{reserve3,jdbcType=VARCHAR},
      </if>
    </set>
    where RECRUIT_ID = #{recruitId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jsunicom.oms.po.CampusRecruitInfo">
    update wo_school_campus_college_recruit_info
    set COLLEGE_ID = #{collegeId,jdbcType=BIGINT},
      GRADE = #{grade,jdbcType=VARCHAR},
      ENROLLMENT_NUMBER = #{enrollmentNumber,jdbcType=INTEGER},
      CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      CREATED_TIME = #{createdTime,jdbcType=TIMESTAMP},
      UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
      UPDATED_TIME = #{updatedTime,jdbcType=TIMESTAMP},
      REMARK = #{remark,jdbcType=VARCHAR},
      RESERVE1 = #{reserve1,jdbcType=VARCHAR},
      RESERVE2 = #{reserve2,jdbcType=VARCHAR},
      RESERVE3 = #{reserve3,jdbcType=VARCHAR}
    where RECRUIT_ID = #{recruitId,jdbcType=BIGINT}
  </update>
  <select id="selectByGroup"  resultMap="BaseResultMap">
    select
    GRADE,
    sum(ENROLLMENT_NUMBER) as ENROLLMENT_NUMBER
    from wo_school_campus_college_recruit_info
    group by GRADE
  </select>
</mapper>