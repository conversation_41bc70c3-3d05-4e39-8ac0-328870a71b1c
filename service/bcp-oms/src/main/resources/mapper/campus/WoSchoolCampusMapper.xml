<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC  "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jsunicom.oms.mapper.campus.CampusDao">

    <!-- ============================= INSERT ============================= -->
    <insert id="save" useGeneratedKeys="true" keyProperty="campusId" >
        INSERT INTO wo_school_campus (campus_id, school_name, campus_name, address, tel, post_code
                        , create_time, update_time, org_code, state, remark)
        VALUES (#{campusId}, #{schoolName}, #{campusName}, #{address}, #{tel}, #{postCode}
              , #{createTime}, #{updateTime}, #{orgCode}, #{state}, #{remark})
    </insert>


    <!-- batch insert for mysql -->
    <insert id="saveBatch">
        INSERT INTO wo_school_campus (campus_id, school_name, campus_name, address, tel, post_code
                        , create_time, update_time, org_code, state, remark)
        VALUES 
        <foreach collection="list" item="item" index="index" separator=",">
              (#{item.campusId}, #{item.schoolName}, #{item.campusName}, #{item.address}, #{item.tel}, #{item.postCode}
             , #{item.createTime}, #{item.updateTime}, #{item.orgCode}, #{item.state}, #{item.remark})
        </foreach>
    </insert>


    <!-- batch insert for oracle -->
    <!--
    <insert id="saveBatch">
        INSERT INTO wo_school_campus(campus_id, school_name, campus_name, address, tel, post_code
                          , create_time, update_time, org_code, state, remark)
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            SELECT #{item.campusId}, #{item.schoolName}, #{item.campusName}, #{item.address}, #{item.tel}, #{item.postCode}
                 , #{item.createTime}, #{item.updateTime}, #{item.orgCode}, #{item.state}, #{item.remark} 
              FROM DUAL 
        </foreach>
    </insert>

    -->

    <!-- ============================= UPDATE ============================= -->
    <update id="update">
        UPDATE wo_school_campus
        <set>
            school_name=#{schoolName},
            campus_name=#{campusName},
            address=#{address},
            tel=#{tel},
            post_code=#{postCode},
            create_time=#{createTime},
            update_time=#{updateTime},
            org_code=#{orgCode},
            state=#{state},
            remark=#{remark},
        </set>
        WHERE campus_id=#{campusId} 
    </update>

    <update id="updateIgnoreNull">
        UPDATE wo_school_campus
        <set>
            <if test="schoolName!= null">school_name=#{schoolName},</if>
            <if test="campusName!= null">campus_name=#{campusName},</if>
            <if test="address!= null">address=#{address},</if>
            <if test="tel!= null">tel=#{tel},</if>
            <if test="postCode!= null">post_code=#{postCode},</if>
            <if test="createTime!= null">create_time=#{createTime},</if>
            <if test="updateTime!= null">update_time=#{updateTime},</if>
            <if test="orgCode!= null">org_code=#{orgCode},</if>
            <if test="state!= null">state=#{state},</if>
            <if test="remark!= null">remark=#{remark},</if>
        </set>
        WHERE campus_id=#{campusId} 
    </update>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index"  separator=";">
            UPDATE wo_school_campus
            <set>
                school_name=#{item.schoolName},
                campus_name=#{item.campusName},
                address=#{item.address},
                tel=#{item.tel},
                post_code=#{item.postCode},
                create_time=#{item.createTime},
                update_time=#{item.updateTime},
                org_code=#{item.orgCode},
                state=#{item.state},
                remark=#{item.remark},
            </set>
            WHERE campus_id=#{item.campusId} 
        </foreach>
    </update>


    <!-- ============================= DELETE ============================= -->
    <delete id="delete">
        DELETE FROM wo_school_campus
        WHERE campus_id=#{campusId} 
    </delete>

    <delete id="deleteBatch">
        DELETE FROM wo_school_campus
        WHERE
        <foreach collection="list" item="item" index="index" open="(" separator="OR" close=")">
            campus_id=#{item.campusId} 
        </foreach>
    </delete>

    <delete id="deleteByPK">
        DELETE FROM wo_school_campus
        WHERE campus_id=#{campusId} 
    </delete>

    <delete id="deleteAll">
        DELETE FROM wo_school_campus
    </delete>


    <!-- ============================= SELECT ============================= -->
    <select id="count" resultType="java.lang.Long">
        SELECT COUNT(*) FROM wo_school_campus
    </select>

    <select id="findByPK" resultType="com.jsunicom.oms.model.campus.Campus">
        SELECT * FROM wo_school_campus
        WHERE campus_id=#{campusId} 
    </select>

    <select id="find" resultType="com.jsunicom.oms.model.campus.Campus">
        SELECT campus_id,school_name,campus_name,address,tel,post_code,create_time,update_time
               ,org_code,state,remark
         FROM wo_school_campus
        <where>
            <if test="campusId!= null">
               AND campus_id = #{campusId}
            </if>
            <if test="schoolName!= null">
               AND school_name = #{schoolName}
            </if>
            <if test="campusName!= null">
               AND campus_name = #{campusName}
            </if>
            <if test="address!= null">
               AND address = #{address}
            </if>
            <if test="tel!= null">
               AND tel = #{tel}
            </if>
            <if test="postCode!= null">
               AND post_code = #{postCode}
            </if>
            <if test="createTime!= null">
               AND create_time = #{createTime}
            </if>
            <if test="updateTime!= null">
               AND update_time = #{updateTime}
            </if>
            <if test="orgCode!= null">
               AND org_code = #{orgCode}
            </if>
            <if test="state!= null">
               AND state = #{state}
            </if>
            <if test="remark!= null">
               AND remark = #{remark}
            </if>
        </where>
    </select>
    
    <select id="getAllCampus" resultType="com.jsunicom.oms.model.campus.Campus">
        SELECT campus_id,school_name,campus_name,address,tel,post_code,create_time,update_time
             ,org_code,state,remark
        FROM wo_school_campus where state = '1' and CAMPUS_ID not in (
            select wscb.CAMPUS_ID from wo_school_campus_bind wscb where wscb.TYPE = 1 and wscb.STATE = 1
        )
    </select>

    <select id="selectCampusList" resultType="com.jsunicom.oms.model.campus.Campus">
        SELECT C.CAMPUS_ID,C.SCHOOL_NAME , C.CAMPUS_NAME FROM WO_SCHOOL_CAMPUS C
        WHERE 1=1
        <if test="orgCodes!= null and orgCodes.size()>0 ">
            AND C.ORG_CODE  in
            <foreach collection="orgCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>

        </if>
        <if test="campusName!= null and campusName != '' ">
            AND CONCAT(C.SCHOOL_NAME , C.CAMPUS_NAME)  LIKE CONCAT('%',#{campusName},'%')
        </if>
    </select>

    <select id="getCampusList" resultType="com.jsunicom.oms.model.campus.Campus"
            parameterType="com.jsunicom.oms.model.campus.CampusBind">
        select
            wsc.campus_id,
            wsc.school_name,
            wsc.campus_name,
            wsc.address,
            wsc.tel,
            wsc.post_code,
            wsc.create_time,
            wsc.update_time,
            wsc.org_code,
            wsc.state,
            wsc.remark
        from
            wo_school_campus_bind wscb
        left join wo_school_campus wsc on wscb.CAMPUS_ID = wsc.CAMPUS_ID
        where
            wscb.bind_id = #{bindId} and wscb.state = '1'
    </select>

    <select id="getSchoolInfoList" resultType="com.jsunicom.oms.model.woshcool.SchoolListInfo"
            parameterType="string">
        select distinct wsi.id,
                        wsi.school_name,
                        wsi.login_logo     schoolLogo,
                        wsi.wlcm_introduce schoolDesc,
                        wsi.wlcm_img       bannerUrl,
                        wscb.CAMPUS_ID,
                        wsi.prod_oper_id    prodOperId,
                        wsi.school_name     merchantName,
                        m.leg_rep_name     legRepName,
                        m.leg_rep_phone    legRepPhone,
                        wsi.id,
                        wsi.login_logo      loginLogo,
                        wsi.wlcm_introduce  wlcmIntroduce,
                        wsi.merchant_Id     merchantId,
                        p.id                partnerId
        from wo_school_info wsi
                 left join wo_school_campus_bind wscb on wsi.MERCHANT_ID = wscb.BIND_ID
                 left join merchant m on m.id = wsi.merchant_Id
                 left join partner p on p.merchant_Id=wsi.MERCHANT_ID and p.is_mer_admin='1'
        where wscb.TYPE = '0'
          and wscb.CAMPUS_ID = #{campusId}
    </select>

    <select id="querySchoolList" resultType="com.jsunicom.oms.model.campus.Campus">
        select
            wsc.campus_id,
            wsc.school_name,
            wsc.campus_name,
            wsc.address,
            wsc.tel,
            wsc.post_code,
            wsc.create_time,
            wsc.update_time,
            wsc.org_code,
            wsc.state,
            wsc.remark
        from
            wo_school_campus_bind wscb
                left join wo_school_campus wsc on wscb.CAMPUS_ID = wsc.CAMPUS_ID
        where
            wscb.bind_id = #{bindId} and wscb.state = '1'  and wscb.type = '1'
    </select>
</mapper>
