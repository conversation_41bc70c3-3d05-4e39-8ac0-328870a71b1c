<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.oms.mapper.ReportMapper">
  <select id="queryMeettingAndActivity" resultType="java.util.Map">
    SELECT tab1.ORG_CODE AS orgCode, tab1.ORG_NAME AS orgName, ifnull(tab2.weeklyMeetingCount, 0) AS weeklyMeetingCount
         , ifnull(tab2.monthlyMeetingCount, 0) AS monthlyMeetingCount
         , ifnull(tab2.otherMeetingCount, 0) AS otherMeetingCount
         , ifnull(tab2.allMeetingCount, 0) AS allMeetingCount
         , ifnull(tab3.teamActivityCount, 0) AS teamActivityCount
         , ifnull(tab3.exchangeActivityCount, 0) AS exchangeActivityCount
         , ifnull(tab3.studyTourActivityCount, 0) AS studyTourActivityCount
         , ifnull(tab3.heterodoxyActivityCount, 0) AS heterodoxyActivityCount
         , ifnull(tab3.otherActivityCount, 0) AS otherActivityCount
         , ifnull(tab3.allActivityCount, 0) AS allActivityCount
    FROM org_info tab1
           LEFT JOIN (
      SELECT t.org_code, sum(CASE WHEN a.RESERVE1 = '01' THEN 1 ELSE 0 END) AS weeklyMeetingCount,
                         sum(CASE WHEN a.RESERVE1 = '02' THEN 1 ELSE 0 END) AS monthlyMeetingCount,
                         sum(CASE WHEN a.RESERVE1 = '03' THEN 1 ELSE 0 END) AS otherMeetingCount,
                         count(1) AS allMeetingCount
      FROM WO_SCHOOL_CAMPUS t
             LEFT JOIN WO_SC_YI_MEETING a ON t.campus_id = a.campus_id
             LEFT JOIN WO_SC_YI_WORK_ORDER b ON a.ORDER_ID = b.ORDER_ID
      WHERE b.ORDER_STATE = '2'
        <if test="startTime != null and endTime != null and startTime != '' and endTime != ''">
          AND a.CREATED_TIME BETWEEN #{startTime} AND #{endTime}
        </if>

      GROUP BY t.org_code
    ) tab2
      ON tab1.org_code = tab2.org_code
    LEFT JOIN (
      SELECT t.org_code, sum(CASE WHEN a.ACTIVITY_TYPE = '01' THEN 1 ELSE 0 END) AS teamActivityCount,
                         sum(CASE WHEN a.ACTIVITY_TYPE = '02' THEN 1 ELSE 0 END) AS exchangeActivityCount ,
                         sum(CASE WHEN a.ACTIVITY_TYPE = '03' THEN 1 ELSE 0 END) AS studyTourActivityCount,
                         sum(CASE WHEN a.ACTIVITY_TYPE = '04' THEN 1 ELSE 0 END) AS heterodoxyActivityCount ,
                         sum(CASE WHEN a.ACTIVITY_TYPE = '05' THEN 1 ELSE 0 END) AS otherActivityCount,
                         count(1) AS allActivityCount
      FROM WO_SCHOOL_CAMPUS t
             LEFT JOIN WO_SC_YI_ACTIVITY a ON t.campus_id = a.campus_id
             LEFT JOIN WO_SC_YI_WORK_ORDER b ON a.ORDER_ID = b.ORDER_ID
      WHERE b.ORDER_STATE = '2'
    <if test="startTime != null and endTime != null and startTime != '' and endTime != ''">
      AND a.CREATED_TIME BETWEEN #{startTime} AND #{endTime}
    </if>
      GROUP BY t.org_code
    ) tab3
    ON tab1.org_code = tab3.org_code
    WHERE tab1.is_city = 'Y'
      <if test="eparchyCode != null and eparchyCode != ''">
        and tab1.org_code =#{eparchyCode}
      </if>
    ORDER BY tab1.order_by
  </select>

  <select id="queryTaskCompletionRate" resultType="java.util.Map">
      select orgCode,orgName,campusCount,channelCampusCount,channelCount,concat(ROUND((channelCampusCount/campusCount)*100,2),'%') channelRate,perfectCount,concat(round((perfectCount/campusCount)*100,2),'%') perfectRate from
      (
          select oi.org_code orgCode,oi.org_name orgName,count(distinct wsc2.campus_id) campusCount,COUNT(distinct wscc.CAMPUS_ID) channelCampusCount,count(distinct wscc.CHANNEL_ID) channelCount,perfectCount from org_info oi
          left join
          (select wsc.ORG_CODE,sum(case when wsce.campus_id is null then 0
          when (coalesce(wsce.IS_BROADBAND_ACCESS,'')='' or wsce.JU_STUDENT is null or wsce.MD_STUDENT is null or wsce.NEW_JU_STUDENT is null or wsce.NEW_MD_STUDENT is null or coalesce(wsce.SCHOOL_CAMPUS_TYPE,'')='' or coalesce(wsce.IS_5G_COVERAGE,'')='' ) then 0
          when wsce.IS_BROADBAND_ACCESS = '1' and (coalesce(wsce.BROADBAND_ACCESS_TYPE,'')='' or coalesce(wsce.BROADBAND_COVERAGE_RATIO,'')='') then 0
          else 1
          end) perfectCount from wo_school_campus wsc left join wo_school_campus_extend wsce on wsc.CAMPUS_ID =wsce.CAMPUS_ID
          <where>
              <if test="eparchyCode != null and eparchyCode != ''">
                  and wsc.org_code=#{eparchyCode}
              </if>
          </where>
          group by wsc.org_code) wsc1 on oi.org_code =wsc1.org_code
          left join wo_school_campus wsc2 on oi.org_code =wsc2.ORG_CODE
          left join wo_school_campus_channel wscc on wsc2.campus_id=wscc.CAMPUS_ID
          where oi.is_city ='Y'
             <if test="id != null and id != ''">
                and wscb.bind_id=#{id}
             </if>
             <if test="eparchyCode != null and eparchyCode != ''">
                 and oi.org_code=#{eparchyCode}
             </if>
           group by oi.org_code,oi.org_name

           order by oi.order_by
          ) T
      union all
      select 'ZZZZ' orgCode,'总计' orgName,ifnull(sum(campusCount),0) campusCount,ifnull(sum(channelCampusCount),0) channelCampusCount,ifnull(sum(channelCount),0) channelCount,concat(ROUND((sum(channelCampusCount)/sum(campusCount))*100,2),'%') channelRate,ifnull(sum(perfectCount),0) perfectCount,concat(round((sum(perfectCount)/sum(campusCount))*100,2),'%') perfectRate from
      (
      select oi.org_code orgCode,oi.org_name orgName,count(distinct wsc2.campus_id) campusCount,COUNT(distinct wscc.CAMPUS_ID) channelCampusCount,count(distinct wscc.CHANNEL_ID) channelCount,perfectCount from org_info oi
      left join
      (select wsc.ORG_CODE,sum(case when wsce.campus_id is null then 0
      when (coalesce(wsce.IS_BROADBAND_ACCESS,'')='' or wsce.JU_STUDENT is null or wsce.MD_STUDENT is null or wsce.NEW_JU_STUDENT is null or wsce.NEW_MD_STUDENT is null or coalesce(wsce.SCHOOL_CAMPUS_TYPE,'')='' or coalesce(wsce.IS_5G_COVERAGE,'')='' ) then 0
      when wsce.IS_BROADBAND_ACCESS = '1' and (coalesce(wsce.BROADBAND_ACCESS_TYPE,'')='' or coalesce(wsce.BROADBAND_COVERAGE_RATIO,'')='') then 0
      else 1
      end) perfectCount from wo_school_campus wsc left join wo_school_campus_extend wsce on wsc.CAMPUS_ID =wsce.CAMPUS_ID
      <where>
          <if test="eparchyCode != null and eparchyCode != ''">
              and wsc.org_code=#{eparchyCode}
          </if>
      </where>
      group by wsc.org_code) wsc1 on oi.org_code =wsc1.org_code
      left join wo_school_campus wsc2 on oi.org_code =wsc2.ORG_CODE
      left join wo_school_campus_channel wscc on wsc2.campus_id=wscc.CAMPUS_ID
      where oi.is_city ='Y'
      <if test="id !=null and id != ''">
          and wscb.bind_id=#{id}
      </if>
      <if test="eparchyCode != null and eparchyCode != ''">
          and oi.org_code=#{eparchyCode}
      </if>
      group by oi.org_code,oi.org_name
      ) T
  </select>

    <select id="queryCompletionRateByEarpchy" resultType="com.jsunicom.oms.dto.report.ReportDto">
        select oi.org_code,oi.org_name,wsc.CAMPUS_ID,wsc.campus_name,p2.partner_name,p2.acct_no,count(distinct wscc.channel_id) channel_count ,
        case when wsce.campus_id is null then '否'
        when (coalesce(wsce.IS_BROADBAND_ACCESS,'')='' or wsce.JU_STUDENT is null or wsce.MD_STUDENT is null or wsce.NEW_JU_STUDENT is null or wsce.NEW_MD_STUDENT is null or coalesce(wsce.SCHOOL_CAMPUS_TYPE,'')='' or coalesce(wsce.IS_5G_COVERAGE,'')=''
        ) then '否'
        when wsce.IS_BROADBAND_ACCESS = '1' and (coalesce(wsce.BROADBAND_ACCESS_TYPE,'')='' or coalesce(wsce.BROADBAND_COVERAGE_RATIO,'')='') then 0
        else '是'
        end perfect_tag
        from wo_school_campus wsc
                 left join org_info oi on wsc.ORG_CODE =oi.org_code
                 left join wo_school_campus_bind wscb  on wsc.CAMPUS_ID =wscb.CAMPUS_ID
                 left join partner p2 on wscb.BIND_ID =p2.id
                 left join wo_school_campus_extend wsce on wsc.CAMPUS_ID =wsce.CAMPUS_ID
                 left join wo_school_campus_channel wscc on wsc.CAMPUS_ID =wscc.CAMPUS_ID and wscc.state='1'
        <where>
            <if test="eparchyCode != null and eparchyCode != ''">
                wsc.ORG_CODE =#{eparchyCode}
            </if>
            <if  test="acctNo != null and acctNo != ''">
                and p2.acct_no =#{acctNo}
            </if>
            <if test="campusId != null and campusId != ''">
                and wsc.campus_id = #{campusId}
            </if>
            <if test="campusName != null and campusName != ''">
                and wsc.campus_name like concat('%',#{campusName},'%')
            </if>
        </where>
        group by wsc.CAMPUS_ID,wsc.SCHOOL_NAME
        union all
        select '' org_code, '总计'org_name,'','','','',ifnull(sum(channelCount),0) channel_count,CAST(ifnull(sum(perfectTag),0) AS CHAR) perfect_tag from
        (select oi.org_name,wsc.CAMPUS_ID,wsc.campus_name,p2.partner_name,p2.acct_no,count(distinct wscc.channel_id) channelCount ,
            case when wsce.campus_id is null then 0
            when (coalesce(wsce.IS_BROADBAND_ACCESS,'')='' or wsce.JU_STUDENT is null or wsce.MD_STUDENT is null or wsce.NEW_JU_STUDENT is null or wsce.NEW_MD_STUDENT is null or coalesce(wsce.SCHOOL_CAMPUS_TYPE,'')='' or coalesce(wsce.IS_5G_COVERAGE,'')=''
            ) then 0
            when wsce.IS_BROADBAND_ACCESS = '1' and (coalesce(wsce.BROADBAND_ACCESS_TYPE,'')='' or coalesce(wsce.BROADBAND_COVERAGE_RATIO,'')='') then 0
            else 1
            end perfectTag
            from wo_school_campus wsc
                      left join org_info oi on wsc.ORG_CODE =oi.org_code
                      left join wo_school_campus_bind wscb  on wsc.CAMPUS_ID =wscb.CAMPUS_ID
                      left join partner p2 on wscb.BIND_ID =p2.id
                      left join wo_school_campus_extend wsce on wsc.CAMPUS_ID =wsce.CAMPUS_ID
                      left join wo_school_campus_channel wscc on wsc.CAMPUS_ID =wscc.CAMPUS_ID and wscc.state='1'
        <where>
            <if test="eparchyCode != null and eparchyCode != ''">
                wsc.ORG_CODE =#{eparchyCode}
            </if>
            <if  test="acctNo != null and acctNo != ''">
                and p2.acct_no =#{acctNo}
            </if>
            <if test="campusId != null and campusId != ''">
                and wsc.campus_id = #{campusId}
            </if>
            <if test="campusName != null and campusName != ''">
                and wsc.campus_name like concat('%',#{campusName},'%')
            </if>
        </where>
             group by wsc.CAMPUS_ID,wsc.SCHOOL_NAME) t
    </select>

    <select id="queryExtendReport" resultType="com.jsunicom.oms.dto.report.ExtendReportDto">
        select oi.org_name,wsc.CAMPUS_ID ,wsc.CAMPUS_NAME,p2.partner_name,p2.acct_no ,count(distinct wscc.CHANNEL_ID) channel_count,
               case when wsce.IS_BROADBAND_ACCESS='1' then '是' when wsce.IS_BROADBAND_ACCESS='1' then '否' else '' end IS_BROADBAND_ACCESS,
               case when wsce.BROADBAND_ACCESS_TYPE='1' then '仅有线' when wsce.BROADBAND_ACCESS_TYPE='2' then '仅无线' when wsce.BROADBAND_ACCESS_TYPE='3' then '有线+无线' else '' end BROADBAND_ACCESS_TYPE,
               case when wsce.BROADBAND_COVERAGE_RATIO ='0' then '低于30%' when wsce.BROADBAND_COVERAGE_RATIO ='1' then '高于30%' else '' end BROADBAND_COVERAGE_RATIO,
               wsce.JU_STUDENT ,wsce.MD_STUDENT ,NEW_JU_STUDENT ,NEW_MD_STUDENT ,
               case when wsce.SCHOOL_CAMPUS_TYPE ='1' then '高等教育' when wsce.SCHOOL_CAMPUS_TYPE ='2' then '中职类教育' when wsce.SCHOOL_CAMPUS_TYPE ='3' then 'K12教育' when wsce.SCHOOL_CAMPUS_TYPE ='4' then '学前教育' when wsce.SCHOOL_CAMPUS_TYPE ='5' then '教培机构' else '' end SCHOOL_CAMPUS_TYPE,
               case when wsce.IS_5G_COVERAGE ='0' then '否' when wsce.IS_5G_COVERAGE ='1' then '是' else '' end  IS_5G_COVERAGE
        from wo_school_campus wsc
                 left join org_info oi on wsc.org_code=oi.org_code
                 left join wo_school_campus_channel wscc on wsc.CAMPUS_ID =wscc.CAMPUS_ID
                 left join wo_school_campus_bind wscb on wsc.CAMPUS_ID =wscb.CAMPUS_ID
                 left join partner p2  on wscb.BIND_ID =p2.id
                 left join wo_school_campus_extend wsce on wsc.CAMPUS_ID =wsce.CAMPUS_ID
        <where>
            <if test="eparchyCode != null and eparchyCode != ''">
                wsc.ORG_CODE =#{eparchyCode}
            </if>
            <if  test="acctNo != null and acctNo != ''">
                and p2.acct_no =#{acctNo}
            </if>
            <if test="campusId != null and campusId != ''">
                and wsc.campus_id = #{campusId}
            </if>
            <if test="campusName != null and campusName != ''">
                and wsc.campus_name like concat('%',#{campusName},'%')
            </if>
        </where>
        group by wsc.CAMPUS_ID ,wsc.CAMPUS_NAME
        union all
        select '总计' org_name,'','','','',ifnull(sum(channel_count),0),'','','',ifnull(sum(JU_STUDENT),0),ifnull(sum(MD_STUDENT),0),ifnull(sum(NEW_JU_STUDENT),0),ifnull(sum(NEW_MD_STUDENT),0),'','' from(
                select oi.org_name,wsc.CAMPUS_ID ,wsc.CAMPUS_NAME,p2.partner_name,p2.acct_no ,count(distinct wscc.CHANNEL_ID) channel_count,
                case when wsce.IS_BROADBAND_ACCESS='1' then '是' when wsce.IS_BROADBAND_ACCESS='1' then '否' else '' end IS_BROADBAND_ACCESS,
                case when wsce.BROADBAND_ACCESS_TYPE='1' then '仅有线' when wsce.BROADBAND_ACCESS_TYPE='2' then '仅无线' when wsce.BROADBAND_ACCESS_TYPE='3' then '有线+无线' else '' end BROADBAND_ACCESS_TYPE,
                case when wsce.BROADBAND_COVERAGE_RATIO ='0' then '低于30%' when wsce.BROADBAND_COVERAGE_RATIO ='1' then '高于30%' else '' end BROADBAND_COVERAGE_RATIO,
                wsce.JU_STUDENT ,wsce.MD_STUDENT ,NEW_JU_STUDENT ,NEW_MD_STUDENT ,
                case when wsce.SCHOOL_CAMPUS_TYPE ='1' then '高等教育' when wsce.SCHOOL_CAMPUS_TYPE ='2' then '中职类教育' when wsce.SCHOOL_CAMPUS_TYPE ='3' then 'K12教育' when wsce.SCHOOL_CAMPUS_TYPE ='4' then '学前教育' when wsce.SCHOOL_CAMPUS_TYPE ='5' then '教培机构' else '' end SCHOOL_CAMPUS_TYPE,
                case when wsce.IS_5G_COVERAGE ='0' then '否' when wsce.IS_5G_COVERAGE ='1' then '是' else '' end  IS_5G_COVERAGE
                from wo_school_campus wsc
                left join org_info oi on wsc.org_code=oi.org_code
                left join wo_school_campus_channel wscc on wsc.CAMPUS_ID =wscc.CAMPUS_ID
                left join wo_school_campus_bind wscb on wsc.CAMPUS_ID =wscb.CAMPUS_ID
                left join partner p2  on wscb.BIND_ID =p2.id
                left join wo_school_campus_extend wsce on wsc.CAMPUS_ID =wsce.CAMPUS_ID
                <where>
                    <if test="eparchyCode != null and eparchyCode != ''">
                        wsc.ORG_CODE =#{eparchyCode}
                    </if>
                    <if  test="acctNo != null and acctNo != ''">
                        and p2.acct_no =#{acctNo}
                    </if>
                    <if test="campusId != null and campusId != ''">
                        and wsc.campus_id = #{campusId}
                    </if>
                    <if test="campusName != null and campusName != ''">
                        and wsc.campus_name like concat('%',#{campusName},'%')
                    </if>
                </where>
                group by wsc.CAMPUS_ID ,wsc.CAMPUS_NAME
         ) t
    </select>

    <!-- 校园渠道发展量报表查询 -->
    <select id="queryCampusChannelReport" resultType="com.jsunicom.oms.dto.report.CampusChannelReportDto">
        SELECT
            CASE a.TRADE_EPARCHY_CODE
                WHEN '0029' THEN '西安'
                WHEN '0910' THEN '咸阳'
                WHEN '0911' THEN '延安'
                WHEN '0912' THEN '榆林'
                WHEN '0913' THEN '渭南'
                WHEN '0914' THEN '商洛'
                WHEN '0915' THEN '安康'
                WHEN '0916' THEN '汉中'
                WHEN '0917' THEN '宝鸡'
                WHEN '0919' THEN '铜川'
                ELSE a.TRADE_EPARCHY_CODE
            END AS tradeEparchyCode,
            a.TRADE_DEPART_ID AS tradeDepartId,
            COUNT(DISTINCT CASE WHEN uu.MEM_USER_ID IS NOT NULL THEN a.USER_ID END) AS prodTypeCpCnt,
            COUNT(DISTINCT CASE WHEN uu.MEM_USER_ID IS NULL AND a.NET_TYPE_CODE != '50' THEN a.USER_ID END) AS prodType40Cnt,
            COUNT(DISTINCT CASE WHEN uu.MEM_USER_ID IS NULL AND a.NET_TYPE_CODE = '50' THEN a.USER_ID END) AS prodType50Cnt
        FROM
            (SELECT
                TBT.TRADE_ID,
                TBT.USER_ID,
                TBT.TRADE_STAFF_ID,
                TBT.TRADE_DEPART_ID,
                TBT.TRADE_CITY_CODE,
                TBT.NET_TYPE_CODE,
                TBT.TRADE_EPARCHY_CODE,
                TBT.EXEC_TIME
             FROM
                DBCRM_PB_84.TF_B_TRADE tbt
             WHERE
                TBT.TRADE_DEPART_ID IN (
                    SELECT x.CHANNEL_ID
                    FROM DBCRM_PB_84_XY.SXXY_PROD_WO_SCHOOL_CAMPUS_CHANNEL x
                )
                AND TBT.cancel_tag = '0'
                AND TBT.NET_TYPE_CODE IN ('40','50')
                <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                    AND TBT.ACCEPT_DATE BETWEEN TO_DATE(#{startTime}, 'YYYY-MM-DD') AND TO_DATE(#{endTime}, 'YYYY-MM-DD')
                </if>
                <if test="eparchyCode != null and eparchyCode != ''">
                    AND TBT.TRADE_EPARCHY_CODE = #{eparchyCode}
                </if>
                <if test="eparchyName != null and eparchyName != ''">
                    AND CASE TBT.TRADE_EPARCHY_CODE
                        WHEN '0029' THEN '西安'
                        WHEN '0910' THEN '咸阳'
                        WHEN '0911' THEN '延安'
                        WHEN '0912' THEN '榆林'
                        WHEN '0913' THEN '渭南'
                        WHEN '0914' THEN '商洛'
                        WHEN '0915' THEN '安康'
                        WHEN '0916' THEN '汉中'
                        WHEN '0917' THEN '宝鸡'
                        WHEN '0919' THEN '铜川'
                        ELSE TBT.TRADE_EPARCHY_CODE
                    END LIKE CONCAT('%', #{eparchyName}, '%')
                </if>
            ) a
        LEFT JOIN
            (SELECT DISTINCT
                MEM_USER_ID,
                END_DATE
             FROM
                DBCRM_PB_84.TF_F_USER_RELATION
             WHERE
                END_DATE > SYSDATE
            ) uu
        ON a.user_id = uu.MEM_USER_ID
        GROUP BY
            a.TRADE_EPARCHY_CODE,
            a.TRADE_DEPART_ID
        ORDER BY
            a.TRADE_EPARCHY_CODE,
            a.TRADE_DEPART_ID
    </select>
</mapper>
