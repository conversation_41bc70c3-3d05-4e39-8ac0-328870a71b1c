<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.task.mapper.photo.PhotoOperateMapper">
    <resultMap id="TfOrdMainMap" type="com.jsunicom.task.po.order.tf.TfOrdMain">
        <id column="ORDER_ID" jdbcType="VARCHAR" property="orderId"/>
        <result column="IN_MODE_CODE" jdbcType="VARCHAR" property="inModeCode"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="STAFF_ID" jdbcType="VARCHAR" property="staffId"/>
        <result column="DEVELOPER_ID" jdbcType="VARCHAR" property="developerId"/>
        <result column="EXT_ORDER_ID" jdbcType="VARCHAR" property="extOrderId"/>
        <result column="ORDER_TIME" jdbcType="TIMESTAMP" property="orderTime"/>
        <result column="ORDER_STATE" jdbcType="VARCHAR" property="orderState"/>
        <result column="CITY_CODE" jdbcType="VARCHAR" property="cityCode"/>
        <result column="EPARCHY_CODE" jdbcType="VARCHAR" property="eparchyCode"/>
        <result column="PROVINCE_CODE" jdbcType="VARCHAR" property="provinceCode"/>
        <result column="DEPART_ID" jdbcType="VARCHAR" property="departId"/>
        <result column="CHANNEL_ID" jdbcType="VARCHAR" property="channelId"/>
        <result column="CHANNEL_TYPE" jdbcType="VARCHAR" property="channelType"/>
        <result column="PAY_MODE" jdbcType="VARCHAR" property="payMode"/>
        <result column="PAY_RESULT" jdbcType="VARCHAR" property="payResult"/>
        <result column="DELIVERY_TYPE" jdbcType="VARCHAR" property="deliveryType"/>
        <result column="ORDER_AMOUNT" jdbcType="VARCHAR" property="orderAmount"/>
        <result column="DISCNT_AMOUNT" jdbcType="VARCHAR" property="discntAmount"/>
        <result column="REAL_AMOUNT" jdbcType="VARCHAR" property="realAmount"/>
        <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="NUMBER_DUPLICATE_FLAG" jdbcType="VARCHAR" property="numberDuplicateFlag"/>
        <result column="ADDRESS_DUPLICATE_FLAG" jdbcType="VARCHAR" property="addressDuplicateFlag"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
    </resultMap>
    <resultMap id="TdOrdOperatorMap" type="com.jsunicom.task.po.order.TdOrdOperator">
        <id property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="operId" column="OPER_ID" jdbcType="VARCHAR"/>
        <result property="sourceSystemId" column="SOURCE_SYSTEM_ID" jdbcType="VARCHAR"/>
        <result property="operatorId" column="OPERATOR_ID" jdbcType="VARCHAR"/>
        <result property="operatorPasswd" column="OPERATOR_PASSWD" jdbcType="VARCHAR"/>
        <result property="province" column="PROVINCE" jdbcType="VARCHAR"/>
        <result property="city" column="CITY" jdbcType="VARCHAR"/>
        <result property="district" column="DISTRICT" jdbcType="VARCHAR"/>
        <result property="channelId" column="CHANNEL_ID" jdbcType="VARCHAR"/>
        <result property="channelType" column="CHANNEL_TYPE" jdbcType="VARCHAR"/>
        <result property="recomPersonId" column="RECOM_PERSON_ID" jdbcType="VARCHAR"/>
        <result property="recomPersonName" column="RECOM_PERSON_NAME" jdbcType="VARCHAR"/>
        <result property="recomDepartId" column="RECOM_DEPART_ID" jdbcType="VARCHAR"/>
        <result property="recomCity" column="RECOM_CITY" jdbcType="VARCHAR"/>
        <result property="eparchyCode" column="EPARCHY_CODE" jdbcType="VARCHAR"/>
        <result property="goodsType" column="GOODS_TYPE" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="TdOrdMDepartMap" type="com.jsunicom.task.entity.TdOrdMDepart">
        <id property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="areaCode" column="area_code" jdbcType="VARCHAR"/>
        <result property="lvlId" column="lvl_id" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="updateStaffId" column="update_staff_id" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
        <result property="eparchyCode" column="eparchy_code" jdbcType="VARCHAR"/>
        <result property="countyCode" column="county_code" jdbcType="VARCHAR"/>
        <result property="channelCode" column="channel_code" jdbcType="VARCHAR"/>
        <result property="departKindCode" column="depart_kind_code" jdbcType="VARCHAR"/>
        <result property="param1" column="param1" jdbcType="VARCHAR"/>
        <result property="param2" column="param2" jdbcType="VARCHAR"/>
        <result property="param3" column="param3" jdbcType="VARCHAR"/>
        <result property="param4" column="param4" jdbcType="VARCHAR"/>
        <result property="vaildTag" column="vaild_tag" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="TdOrdMAreaMap" type="com.jsunicom.task.entity.TdOrdMArea">
        <id property="code" column="code" jdbcType="VARCHAR"/>
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="lvlId" column="lvl_id" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="VARCHAR"/>
        <result property="pos" column="pos" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="updateStaffId" column="update_staff_id" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
        <result property="code1" column="code1" jdbcType="VARCHAR"/>
        <result property="code2" column="code2" jdbcType="VARCHAR"/>
        <result property="code3" column="code3" jdbcType="VARCHAR"/>
        <result property="code4" column="code4" jdbcType="VARCHAR"/>
        <result property="code5" column="code5" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="querySynPhotoRecords" resultType="hashmap" parameterType="java.lang.String">
        select *
        from TL_ORD_SYNPHOTO_RECORD
        where syn_state in ('0', '2')
    </select>
    <select id="selectTfOrdMain" resultMap="TfOrdMainMap" parameterType="java.lang.String">
        select *
        from TF_ORD_MAIN
        where order_id = #{orderId}
    </select>
    <select id="selectTdOrdOperatorList" resultMap="TdOrdOperatorMap" parameterType="java.lang.String">
        select *
        from TD_ORD_OPERATOR
        where eparchy_code = #{eparchyCode}
    </select>
    <select id="selectTdOrdMDepart" resultMap="TdOrdMDepartMap" parameterType="java.lang.String">
        select *
        from td_ord_m_depart
        where `code` = #{departCode}
    </select>
    <select id="selectTdOrdMArea" resultMap="TdOrdMAreaMap" parameterType="java.lang.String">
        select *
        from td_ord_m_area
        where `code` = #{code}
    </select>
    <select id="queryLatestActiveRecord" parameterType="java.lang.String" resultType="hashmap">
        select temp.*
        from (select *
              from TF_ORD_SVCNUM_ACTIVE s
              where s.order_Id = #{orderId}
              order by s.create_date desc) temp limit 1
    </select>
    <select id="queryPhotosByActivieId" parameterType="java.lang.String" resultType="hashmap">
        select *
        from TF_ORD_SVCNUM_ACTIVE_RES
        where ACTIVIE_ID = #{activieId}
          and RES_TYPE in ('1','3')
    </select>
    <insert id="insertSynPhotoRecord" parameterType="hashmap">

        insert into TL_ORD_SYNPHOTO_RECORD (order_id, activie_id, syn_state, create_date, update_date, serial_number,
                                            cust_name, pspt_id, error_msg)
        values (#{orderId}, #{activieId}, #{synState}, now(), now(), #{serialNumber}, #{custName}, #{custId},
                #{synMsg})

    </insert>
    <insert id="saveSynPhotoInfoZRR" parameterType="hashmap">
        insert into tl_ord_synphoto_info(ORDER_ID,WSL_ORDER_ID,SYN_STATE,TRADE_ID,EPARCHY_CODE,SERIAL_NUMBER,PSPT_ID,
                                         PSPT_NAME,DEPART_ID,DEPART_NAME,CHANNEL_ID,STAFF_ID,INPUT_TIME,SOURCE_SYSTEM,OPER_TYPE,CERT_TYPE,PHOTO_NAME1,
                                         PHOTO_NAME1A,PHOTO_NAME1B,PHOTO_URL1A,PHOTO_URL1B,PHOTO_URL1,ORDER_DATE,TRADE_TYPE_CODE,UPDATE_TIME,TRADE_DATE,PHOTO_NAME11,PHOTO_URL11)
        values
            (#{orderId}, #{wslPhotoOrderId}, '0', #{tradeId},
             #{eparchyCode}, #{serialNumber}, #{custId}, #{custName}, #{departId}, #{departName}, #{channelId}, #{staffId},
             now(), #{sourceSystem}, #{operType}, #{certType}, #{photoName1}, #{photoName1a}, #{photoName1b}, #{photoUrl1a}, #{photoUrl1b}, #{photoUrl1}, #{orderDate}, #{tradeTypeCode}, now(), #{tradeDate},
             #{photoName11}, #{photoUrl11})

    </insert>
    <update id="updateSynPhotoRecordState" parameterType="hashmap">

        update TL_ORD_SYNPHOTO_RECORD set

        syn_state =#{synState}

        <if test="serialNumber!=null and serialNumber !=''">
            ,serial_number=#{serialNumber}
        </if>
        <if test="custName!=null and custName !=''">
            ,cust_name=#{custName}
        </if>
        <if test="custId!=null and custId !=''">
            ,pspt_id=#{custId}
        </if>
        <if test="synMsg!=null and synMsg !=''">
            ,error_msg=#{synMsg}
        </if>
        , update_date =now()
        where order_id=#{orderId}
        <if test="activieId!=null ">
            and activie_id=#{activieId}
        </if>
    </update>
    <select id="querySynPhotoZRR" parameterType="java.lang.String" resultType="hashmap">
        select '34'          as PROVINCE_CODE,
               a.ORDER_ID    as PRE_TRADE_ID,
               a.WSL_ORDER_ID,
               a.SYN_STATE,
               a.TRADE_ID,
               b.param_key as EPARCHY_CODE,
               a.SERIAL_NUMBER,
               a.PSPT_ID,
               a.PSPT_NAME,
               a.DEPART_ID,
               a.DEPART_NAME,
               a.CHANNEL_ID,
               a.STAFF_ID,
               a.INPUT_TIME,
               SOURCE_SYSTEM,
               OPER_TYPE,
               a.CERT_TYPE,
               a.PHOTO_NAME1,
               a.PHOTO_NAME1A,
               a.PHOTO_NAME1B,
               a.PHOTO_URL1A,
               a.PHOTO_URL1B,
               a.PHOTO_URL1,
               ORDER_DATE    as PRE_TRADE_TIME,
               TRADE_DATE    as TRADE_TIME,
               TRADE_TYPE_CODE,
               UPDATE_TIME,
               a.PHOTO_NAME11,
               a.PHOTO_URL11,
               oc.SIMILARITY as PHOTO_NAME1_SIMILARITY,
               '0' as PHOTO_NAME1_SIMILARITY_TYPE
        from tl_ord_synphoto_info a,
             td_m_sys_dict b,
             tf_ord_custinfo oc
        where a.syn_state in ('0', '2')
          and a.eparchy_code = b.param1
          and a.order_id = oc.order_id
          and b.param_type = 'cityCode'
          and a.SOURCE_SYSTEM = #{sourceSystem}
    </select>
    <update id="updatePhotoInfoStateZRR" parameterType="hashmap">
        update tl_ord_synphoto_info set syn_state=#{synState}
        <if test="fileName!=null and fileName !=''">
            ,FILE_NAME=#{fileName}
        </if>
        ,update_time =now() where order_id=#{orderId}
    </update>
    <insert id="saveRspPhotoInfoZRR" parameterType="hashmap">
        insert into TL_ORD_SYNPHOTO_INFO_RSP(ORDER_ID,DEAL_TAG,TRADE_ID,EPARCHY_CODE,RESP_CODE,RESP_DESC,
		INPUT_TIME,UPDATE_TIME,SOURCE_SYSTEM,FILE_NAME)
        values
            (#{orderId}, '0', #{tradeId},
            #{eparchyCode}, #{respCode}, #{respDesc}, now(), now(), #{sourceSystem}, #{fileName})

    </insert>
    <update id="updateRspRecordSucess">
        update TL_ORD_SYNPHOTO_INFO_RSP
        set DEAL_TAG    ='1',
            UPDATE_TIME =now()
        WHERE DEAL_TAG = '0'
          and RESP_CODE = 'F000'
    </update>
    <select id="queryRspNoDealFail" resultType="hashmap">
        select a.ORDER_ID,
               a.DEAL_TAG,
               a.TRADE_ID,
               a.EPARCHY_CODE,
               a.RESP_CODE,
               a.RESP_DESC,
               a.INPUT_TIME,
               a.UPDATE_TIME,
               a.SOURCE_SYSTEM,
               a.FILE_NAME
        from TL_ORD_SYNPHOTO_INFO_RSP a
        WHERE DEAL_TAG = '0'
          and RESP_CODE!='F000'
        order by a.INPUT_TIME
    </select>
    <update id="updatePhotoInfoStateRsp" parameterType="hashmap">

        update tl_ord_synphoto_info set syn_state=#{synState}
        ,update_time =now() where 1=1
        <if test="fileNameReq!=null and fileNameReq !=''">
            and FILE_NAME=#{fileNameReq}
        </if>
        <if test="tradeId!=null and tradeId !=''">
            and TRADE_ID=#{tradeId}
        </if>

    </update>
    <update id="updateRspRecordState" parameterType="hashmap">
        update TL_ORD_SYNPHOTO_INFO_RSP set
        DEAL_TAG =#{dealTag}
        , UPDATE_TIME =now()
        WHERE 1=1 and DEAL_TAG='0'
        <if test="fileName!=null and fileName !=''">
            and FILE_NAME=#{fileName}
        </if>
        <if test="tradeId!=null and tradeId !=''">
            and TRADE_ID=#{tradeId}
        </if>
    </update>

    <update id="updateActiveResource" parameterType="hashmap">

        update TF_ORD_SVCNUM_ACTIVE_RES set
        RES_TYPE ='1'
        <if test="url!=null and url !=''">
            ,URL=#{url}
        </if>
        where ID=#{id}

    </update>
    <select id="qryPhotosByActivieIdAndType" parameterType="java.lang.String" resultType="hashmap">
        select *
        from TF_ORD_SVCNUM_ACTIVE_RES
        where ACTIVIE_ID = #{activieId}
          and RES_TYPE = #{resType}
    </select>
</mapper>
