<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsunicom.task.mapper.order.SmsMapper">

	<sql id="Base_Column_List">
         TEMPLATE_ID templateId,TEMPLATE_NAME templateName,TEMPLATE_TYPE templateType,
         ORDER_NODE orderNode,RECEIVER_TYPE receiverType,MSG_ID msgId,CONTENT content ,CONTENT_YW contentYw,REMAR<PERSON> remark,SEND_PARAMS_YW sendParamsYw
    </sql>
	<select id="selectByTemplateId" resultType="com.jsunicom.task.po.order.TdOrdSmsTemplate">
		select <include refid="Base_Column_List"/>
		from td_ord_sms_template
		where TEMPLATE_ID = #{templateId}
	</select>

	<insert id="insertSmsWork" parameterType="hashmap">
		INSERT INTO tf_ord_sms_work(LOG_TIME,ORDER_ID,ORDER_NODE,DEAL_TAG,SEND_OBJECT,SERIAL_NUMBER,STAFF)
			VALUES(now(),#{orderId},#{orderNode},'0',#{sendObject},#{serialNumber},#{staff})
	</insert>


</mapper>
