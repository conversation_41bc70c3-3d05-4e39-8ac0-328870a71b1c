package com.jsunicom.task.po.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 搬运自老校园orderreceive
 * <AUTHOR>
 * */

@ApiModel(description = "订单列表查询返回报文商品信息")
@Data
public class OrderListGoodsRsp implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "商品订单编码", required = true, position = 1)
	private String goodsOrderId;

//	@ApiModelProperty(value = "商品订单状态", required = true, position = 2)
//	private String goodsState;

	@ApiModelProperty(value = "商品编码", required = true, position = 3)
	private String goodsId;

	@ApiModelProperty(value = "商品名称", required = true, position = 4)
	private String goodsName;

	@ApiModelProperty(value = "商品描述", required = true, position = 5)
	private String goodsDesc;

	@ApiModelProperty(value = "商品类型", required = true, position = 6)
	private String goodsType;

//	@ApiModelProperty(value = "商品图片URL", required = true, position = 7)
//	private String goodsUrl;

//	@ApiModelProperty(value = "触点商品详情URL", required = true, position = 8)
//	private String extWebUrl;

	@ApiModelProperty(value = "入网信息", required = true, position = 9)
	private List<NetworkNumberRsp> networkNumberList;

	@ApiModelProperty(value = "商品客户信息", required = true, position = 10)
	private List<GoodsCustInfoRsp> goodsCustInfo;

}
