package com.jsunicom.task.timer.order;

import com.jsunicom.task.service.UploadBuildInfoService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023-09-04-19:55
 */
@Slf4j
@Component
public class DownloadIntoDBJob {
    @Autowired
    private UploadBuildInfoService uploadBuildInfoService;

    @XxlJob("downloadIntoDBJob")
    public ReturnT<String> downloadIntoDBJob(String param) {
        log.info("下载数据入库");
        try {
            uploadBuildInfoService.pullReconiliation();
        }catch(Exception e){
            e.printStackTrace();
            log.error("文件下载入库失败:" + e.getMessage());
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
