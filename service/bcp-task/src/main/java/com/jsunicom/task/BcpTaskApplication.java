package com.jsunicom.task;

import com.jsunicom.task.timer.order.SmsAndFaceAbilityJob;
import com.jsunicom.task.utils.S3Properties;
import com.jsunicom.task.utils.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.bind.annotation.CrossOrigin;

import javax.annotation.Resource;
import java.util.Scanner;

/**
 * @Title: BcpTaskApplication
 * <AUTHOR>
 * @Package com.jsunicom.task
 * @Date 2023/12/18 15:46
 * @description:
 */
@Slf4j
@CrossOrigin
@EnableDiscoveryClient
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
@MapperScan("com.jsunicom.task.mapper")
@EnableConfigurationProperties({S3Properties.class})
@ComponentScan(basePackages = {
        "com.jsunicom.common.*",
        "com.jsunicom.task.*"
})
@EnableFeignClients(basePackages = {
        "com.jsunicom.task.feign"
})
public class BcpTaskApplication {


    public static void main(String[] args) {
        SmsAndFaceAbilityJob smsAndFaceAbilityJob=new SmsAndFaceAbilityJob();
        SpringApplication.run(BcpTaskApplication.class, args);

       /* Scanner scanner=new Scanner(System.in);
        String str = scanner.next();
        String s = smsAndFaceAbilityJob.checkNumberNet(str);
        System.out.println(s);*/

//        ApplicationContext ac = SpringApplication.run(BcpTaskApplication.class, args);
//        SpringUtil.initContext(ac);
    }
}
