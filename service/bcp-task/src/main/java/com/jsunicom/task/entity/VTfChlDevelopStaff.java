package com.jsunicom.task.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * @Author: yjh
 * @Version: V1.00
 * @Date: Created in  2023/4/11 10:34
 * @Since: V1.00
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class VTfChlDevelopStaff implements Serializable {
    private static final long serialVersionUID = 1L;
    private String devCode;
    private String devName;
    private String chnlCode;
    private String chnlId;
    private String linkmanPhone;
    private String tradeEparchyCode;

    public static final String DEV_CODE = "DEV_CODE";
    public static final String DEV_NAME = "DEV_NAME";
    public static final String CHNL_CODE = "CHNL_CODE";
    public static final String CHNL_ID = "CHNL_ID";
    public static final String LINKMAN_PHONE = "LINKMAN_PHONE";
    public static final String TRADE_EPARCHY_CODE = "TRADE_EPARCHY_CODE";
}
