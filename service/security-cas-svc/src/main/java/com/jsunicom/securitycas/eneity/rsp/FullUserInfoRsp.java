package com.jsunicom.securitycas.eneity.rsp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(description = "查询指定员工详细信息响应参数节点")
public class FullUserInfoRsp implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", required = true, position = 1)
    private String userPid;

    @ApiModelProperty(value = "员工工号", required = true, position = 2)
    private String staffNo;

    @ApiModelProperty(value = "姓名", required = true, position = 3)
    private String staffName;

    @ApiModelProperty(value = "性别", required = true, position = 4)
    private String sex;

    @ApiModelProperty(value = "手机号码", required = true, position = 5)
    private String serialNumber;

    @ApiModelProperty(value = "省资源中心手机号码", required = true, position = 6)
    private String ncSerialNumber;

    @ApiModelProperty(value = "所属省份编码", required = true, position = 7)
    private String province;

    @ApiModelProperty(value = "部门或渠道名称", required = true, position = 8)
    private String departOrChnlName;

    @ApiModelProperty(value = "离职状态 0：正常 1：离职 2：冻结", required = true, position = 9)
    private String dimissionTag;

    @ApiModelProperty(value = "部门性质", required = true, position = 10)
    private String departKindType;

    @ApiModelProperty(value = "员工类别", required = true, position = 11)
    private String staffClass;

    @ApiModelProperty(value = "部门编码", required = true, position = 12)
    private String departCode;

    @ApiModelProperty(value = "所属地市编码", required = true, position = 13)
    private String areaCode;
}
