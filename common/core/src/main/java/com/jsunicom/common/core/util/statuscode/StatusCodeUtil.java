package com.jsunicom.common.core.util.statuscode;

import com.jsunicom.common.core.exception.BusinessException;
import com.jsunicom.common.core.util.Result;

/**
 * <AUTHOR>
 * @Title: SysStatusCode
 * @ProjectName zt
 * @Description: 状态码管理
 * @date 2019/4/239:22
 */


public class StatusCodeUtil {


    /**
     * 通过codeEn获取获取状态码
     *
     * @param codeEn 状态码英文
     * @return
     */
    public static Result getResultByStatusCodeEn(String codeEn) {
        return SysStatusCodeToResult(getStatusCodeBycCodeEnFromEnum(codeEn));
    }


    private static StatusCode getStatusCodeBycCodeEnFromEnum(String codeEn) {
        StatusCode statusCode = new StatusCode();
        statusCode.setCodeEn(StatusCodeEnEnum.valueOf(codeEn).name());
        statusCode.setCode(StatusCodeEnEnum.valueOf(codeEn).getCode());
        statusCode.setSuccess(StatusCodeEnEnum.valueOf(codeEn).getSuccess());
        statusCode.setDesc(StatusCodeEnEnum.valueOf(codeEn).getDesc());
        statusCode.setMsg(StatusCodeEnEnum.valueOf(codeEn).getMsg());
        statusCode.setAtcion(StatusCodeEnEnum.valueOf(codeEn).getAtcion());
        statusCode.setModule(StatusCodeEnEnum.valueOf(codeEn).getModule());
        statusCode.setObj(StatusCodeEnEnum.valueOf(codeEn).getObj());
        return statusCode;
    }


    /**
     * codestatus转换成Result
     *
     * @param statusCode
     * @return
     */
    public static Result SysStatusCodeToResult(StatusCode statusCode) throws BusinessException {
        return new Result(statusCode.getCode(), statusCode.getCodeEn(), statusCode.getSuccess(), statusCode.getDesc(), statusCode.getMsg(), statusCode.getProcessId());
    }
}
