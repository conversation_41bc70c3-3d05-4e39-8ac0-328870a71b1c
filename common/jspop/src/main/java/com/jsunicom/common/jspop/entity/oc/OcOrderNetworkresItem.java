package com.jsunicom.common.jspop.entity.oc;

import com.jsunicom.common.core.entity.po.BasePO;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-02-14 14:20:49
 * Copyright 订单管理
*/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OcOrderNetworkresItem extends BasePO implements Serializable {
 	/**
     * 序列
     */
    private static final long serialVersionUID = 1L;

    /**
     * 
    */
	private Long orderId;
    /**
     * 
    */
	private Long orderLineId;
    /**
     * 
    */
	private Long itemId;
    /**
     * 
    */
	private String attrType;
    /**
     * 
    */
	private String attrCode;
    /**
     * 
    */
	private String valueName;
    /**
     * 
    */
	private String attrValue;
    /**
     * 
    */
	private Date startDate;
    /**
     * 
    */
	private Date endDate;
    /**
     * 0－增加  1－删除  2－修改
    */
	private String modifyTag;
    /**
     * 
    */
	private String provinceCode;
}