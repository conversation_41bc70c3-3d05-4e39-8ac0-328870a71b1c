package com.jsunicom.common.jslt.message.common.enums;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Created by T<PERSON>ca<PERSON>ongteng on 2018/3/5.
 */
public class Message {

    private List<String> receivers;//消息接受者列表 ,手机短信发送时，只填写一个号码，如果多填，内容按第一个号码发送
    private String sender;//消息来源
    private MessageType type;//消息类型枚举类型(短信,企业微信)
    //private String QyMessageType ;//企业微信消息类型(TEXT 文本  NEWS  图文)
    private HashMap<String,Object> params;//消息内容参数
    //private Object messageBody;//自定义包体 如:企业微信图文类
    private String createTime;//消息创建时间 2018-03-01 13:32:33
    /**
     * 创建时间2018-11-21 11：12:00,发送类型，指定短信发送的渠道
     * 目前已有渠道1/2,1为已有发送短信渠道；2为短信调度平台发送短信渠道
     */
    private String sendType;
    /**
     * 创建时间2018-11-22 11：17:00,发送模板，当发送类型为1时，生效；新的短信发送需要指定模板id
     */
    private String templateId;
    /**
     *
     *
     */
    public Message() {

    }

    /**
     *
     * @param receivers  消息接受者列表
     * @param sender 消息来源
     * @param type 消息类型枚举类型(短信,企业微信)
     * @param params  消息内容参数
     */
    public Message(List<String> receivers, String sender, MessageType type, HashMap<String, Object> params) {
        this.receivers = receivers;
        this.sender = sender;
        this.type = type;
        this.params = params;
        this.createTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:SS").format(new Date());
    }
    public List<String> getReceivers() {
        return receivers;
    }
    public void setReceivers(List<String> receivers) {
        this.receivers = receivers;
    }

    public String getSender() {
        return sender;
    }
    public void setSender(String sender) {
        this.sender = sender;
    }
    public MessageType getType() {
        return type;
    }
    public void setMessageType(MessageType type) {
        this.type = type;
    }
    /* public String getQyMessageType() {
         return QyMessageType;
     }
     public void setQyMessageType(String QyMessageType) {
         this.QyMessageType = QyMessageType;
     }*/
    /*public Object getmessageBody() {
        return messageBody;
    }
    public void setmessageBody(Object messageBody) {
        this.messageBody = messageBody;
    }*/
    public HashMap<String, Object> getParams() {
        return params;
    }
    public void setParams(HashMap<String, Object> params) {
        this.params = params;
    }

	public String getSendType() {
		return sendType;
	}

	public void setSendType(String sendType) {
		this.sendType = sendType;
	}

	public String getTemplateId() {
		return templateId;
	}

	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}


}
