package com.jsunicom.common.jslt.message.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.httpclient.ConnectTimeoutException;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.params.CoreConnectionPNames;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Repository;

import java.io.UnsupportedEncodingException;
import java.net.SocketTimeoutException;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Locale;

@Repository
public class RequestUtil {
    private static final Log logger = LogFactory.getLog(RequestUtil.class);

    /**
     * 签名生成
     *
     * @param httpPost
     * @param contentType
     * @param currentDate
     * @return
     */
    private static String getSignature(HttpPost httpPost,String contentType,String currentDate, String screct) {
        String strBackUrl = httpPost.getURI().getPath();
        if (httpPost.getURI().getQuery() != null) {
            strBackUrl += "?" + (httpPost.getURI().getQuery()); // 参数
        }
        String toSign = httpPost.getMethod() + "\n" + "" + "\n"
                + contentType+ "\n" + currentDate + "\n"
                + strBackUrl;
        //System.out.println("path:   "+toSign);


        return SignUtil.hmac_sha1(toSign, screct);
    }




    /**
     * 信息解码
     *
     * @param val
     * @return
     * @throws UnsupportedEncodingException
     */
    private String decodeMsg(String val) throws UnsupportedEncodingException {
        if(StringUtils.isEmpty(val)) {
            return val;
        }
        return URLDecoder.decode(val,"UTF-8");
    }

    /**
     * json 数据转换
     *
     * @param data
     * @return
     */
    public JSONObject getJsonData(String data) {
        JSONObject jsonObj = null;
        try {
            if (StringUtils.isNotEmpty(data)) {
                jsonObj = JSON.parseObject(data);
            } else {
                logger.error("接口没有返回数据:data[" + data + "]");
            }
        } catch (JSONException e) {
            logger.error("数据解析错误", e);
        }
        return jsonObj;
    }

    /**
     * 接口调用
     *
     * @param url
     *            接口请求地址
     * @param params
     *            参数集合
     * @return
     * @throws Exception
     */
    public String sendRequest(String url,List<NameValuePair> params, String apiKey, String screct)   {
        String result = "";
        CloseableHttpClient httpClient=null;
		try {
            System.out.println("调用接口参数数据：params="+params);

            // 创建HttpPost对象
            HttpPost httpPost = new HttpPost(url);
            //内容类型
            String contentType = "application/x-www-form-urlencoded;charset=utf-8;";
            //请求时间
            String currentDate  = getUTCDate();

            //签名生成
            String signature = getSignature(httpPost,contentType,currentDate,screct);
            httpPost.addHeader("Date", currentDate);
            httpPost.addHeader("Content-MD5", "");
            httpPost.addHeader("Authorization", "CUCC" + " " + apiKey + ":" + signature);
            httpPost.addHeader("apikey", apiKey);

            UrlEncodedFormEntity entity = new UrlEncodedFormEntity(params, HTTP.UTF_8);
            entity.setContentType(contentType);
            httpPost.setEntity(entity);

            httpClient = HttpClients.createDefault();

            httpClient.getParams().setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT,50000);//连接时间
            httpClient.getParams().setParameter(CoreConnectionPNames.SO_TIMEOUT,50000);//数据传输时间

            HttpResponse httpResponse = httpClient.execute(httpPost);

            System.out.println("statuscode = " + httpResponse.getStatusLine().getStatusCode());

            if (httpResponse.getStatusLine().getStatusCode() == 200) {
                HttpEntity httpEntity = httpResponse.getEntity();
                result = decodeMsg(EntityUtils.toString(httpEntity));// 取出应答字符串
            }else{
                throw new Exception("调用接口出现错误:statuscode="+httpResponse.getStatusLine().getStatusCode());
            }

		} catch (SocketTimeoutException e) {
			logger.error("接口调用超时", e);

		}catch (ConnectTimeoutException e) {
		    logger.error("请求接口超时", e);
        }catch (Exception e) {
			logger.error("接口调用异常", e);

		}finally {
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
            }catch (Exception e){
                logger.error("httpClient关闭失败，原因是",e);
            }
        }

        return result;
    }

    /**
     * 获取系统UTC 时间
     *
     * @return
     */
    public String getUTCDate() {
        //1、取得本地时间：
        final java.util.Calendar cal = java.util.Calendar.getInstance();
        //System.out.println(cal.getTime());
        //2、取得时间偏移量：
        final int zoneOffset = cal.get(java.util.Calendar.ZONE_OFFSET);
        //System.out.println(zoneOffset);
        //3、取得夏令时差：
        final int dstOffset = cal.get(java.util.Calendar.DST_OFFSET);
        //System.out.println(dstOffset);
        //4、从本地时间里扣除这些差量，即可以取得UTC时间：
        cal.add(java.util.Calendar.MILLISECOND, -(zoneOffset + dstOffset));
        //System.err.println(cal.getTime());
        System.out.println(cal.getTime());
        return new SimpleDateFormat("EEE, d MMM yyyy HH:mm:ss z", Locale.US).format(cal.getTime());
    }

    public String responseMsg(JSONObject json) throws JSONException{
        String errorMsg = "未知错误";
        if(json==null){
            errorMsg = "服务器没有响应";
        }else{
            if(json.containsKey("result")){
                int result = Integer.parseInt(json.getString("result"));
                switch (result) {
                    case 0:
                        errorMsg = "0";
                        break;
                    case 1403:
                        errorMsg = "1403";
                        break;
                    case 9999:
                        errorMsg = "1403";
                        break;
                    case 10000:
                        errorMsg = "0";
                        break;
                    default:
                        errorMsg = json.getString("err_msg");
                        logger.error(errorMsg);
                        break;
                }
            }else{
                //cbss查询失败或者是没有数据等等
                if(json.containsKey("message")){
                    JSONObject obj = json.getJSONObject("message");
                    int result = Integer.parseInt(obj.getString("error"));
                    switch (result) {
                        case 0:
                            errorMsg = "0";
                            break;
                        case 1403:
                            errorMsg = "1403";
                            break;
                        case 9999:
                            errorMsg = "1403";
                            break;
                        case 10000:
                            errorMsg = "0";
                            break;
                        default:
                            errorMsg = json.getString("err_msg");
                            logger.error(errorMsg);
                            break;
                    }
                }
            }
        }
        return errorMsg;
    }
}

